#!/bin/bash

# AI-Powered Wisdom Deployment Script
# This script helps prepare and deploy the application

echo "🚀 AI-Powered Wisdom Deployment Helper"
echo "======================================"

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

echo "📋 Pre-deployment checklist:"
echo ""

# Check if frontend directory exists
if [ -d "frontend" ]; then
    echo "✅ Frontend directory found"
else
    echo "❌ Frontend directory not found"
    exit 1
fi

# Check if vercel.json exists
if [ -f "vercel.json" ]; then
    echo "✅ vercel.json configuration found"
else
    echo "❌ vercel.json not found"
    exit 1
fi

# Check if config.js exists
if [ -f "frontend/src/config.js" ]; then
    echo "✅ Frontend config.js found"
else
    echo "❌ Frontend config.js not found"
    exit 1
fi

# Check if .env.example exists
if [ -f "frontend/.env.example" ]; then
    echo "✅ Frontend .env.example found"
else
    echo "❌ Frontend .env.example not found"
    exit 1
fi

echo ""
echo "🔧 Preparing for deployment..."

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
npm install

# Build frontend to test
echo "🏗️ Testing frontend build..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Frontend build successful"
else
    echo "❌ Frontend build failed"
    exit 1
fi

cd ..

echo ""
echo "✅ All checks passed! Ready for deployment."
echo ""
echo "📝 Next steps:"
echo "1. Push your code to GitHub:"
echo "   git add ."
echo "   git commit -m 'Prepare for Vercel deployment'"
echo "   git push origin main"
echo ""
echo "2. Deploy backend to Railway/Render/Heroku"
echo "   See VERCEL_DEPLOYMENT.md for detailed instructions"
echo ""
echo "3. Deploy frontend to Vercel:"
echo "   - Go to vercel.com/dashboard"
echo "   - Import your GitHub repository"
echo "   - Set root directory to 'frontend'"
echo "   - Add environment variables"
echo "   - Deploy!"
echo ""
echo "📖 For detailed instructions, see VERCEL_DEPLOYMENT.md"
echo ""
echo "🎉 Good luck with your deployment!"
