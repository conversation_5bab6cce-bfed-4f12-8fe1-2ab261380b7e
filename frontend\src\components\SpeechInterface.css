.speech-interface {
  padding: 2rem;
  height: 100%;
  overflow-y: auto;
}

.speech-header {
  text-align: center;
  margin-bottom: 2rem;
}

.speech-header h2 {
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.speech-header p {
  color: #718096;
  margin: 0;
}

.speech-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.interaction-selector label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #4a5568;
}

.interaction-selector select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  color: #2d3748;
}

.speech-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
}

.speech-toggle input {
  margin: 0;
}

.recording-section {
  text-align: center;
  margin-bottom: 2rem;
}

.recording-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.record-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: #48bb78;
  border: none;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.record-button:hover:not(:disabled) {
  background: #38a169;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(72, 187, 120, 0.4);
}

.record-button.recording {
  background: #e53e3e;
  animation: pulse 2s infinite;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.record-button.recording:hover {
  background: #c53030;
  box-shadow: 0 6px 16px rgba(229, 62, 62, 0.4);
}

.process-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #667eea;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.process-button:hover:not(:disabled) {
  background: #5a67d8;
}

.process-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.playback-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f093fb;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-right: 0.75rem;
}

.playback-button:hover:not(:disabled) {
  background: #e879f9;
}

.playback-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.clear-button {
  padding: 0.75rem 1.5rem;
  background: #718096;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.clear-button:hover:not(:disabled) {
  background: #4a5568;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #e53e3e;
  font-weight: 500;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: #e53e3e;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.transcribing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #3182ce;
  font-weight: 500;
  margin-top: 1rem;
}

.transcribing-indicator .pulse-dot {
  background: #3182ce;
}

.processing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #805ad5;
  font-weight: 500;
  margin-top: 1rem;
}

.processing-indicator .spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.transcription-section,
.response-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.transcription-section h3,
.response-section h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.audio-controls {
  display: flex;
  gap: 0.5rem;
}

.speak-button,
.stop-speak-button {
  padding: 0.5rem;
  background: #667eea;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speak-button:hover:not(:disabled) {
  background: #5a67d8;
}

.speak-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.stop-speak-button {
  background: #e53e3e;
}

.stop-speak-button:hover {
  background: #c53030;
}

.transcription-text,
.response-text {
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  line-height: 1.6;
  color: #2d3748;
}

/* Markdown styling for AI responses */
.response-text h1,
.response-text h2,
.response-text h3,
.response-text h4,
.response-text h5,
.response-text h6 {
  margin: 1rem 0 0.5rem 0;
  color: #2d3748;
  font-weight: 600;
}

.response-text h1 { font-size: 1.5rem; }
.response-text h2 { font-size: 1.3rem; }
.response-text h3 { font-size: 1.1rem; }

.response-text strong {
  font-weight: 700;
  color: #2d3748;
}

.response-text em {
  font-style: italic;
  color: #4a5568;
}

.response-text p {
  margin: 0.5rem 0;
}

.response-text ul,
.response-text ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.response-text li {
  margin: 0.25rem 0;
}

.response-text blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #4a5568;
}

.response-text code {
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.speaking-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  color: #667eea;
  font-weight: 500;
}

.audio-wave {
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  animation: wave 1s ease-in-out infinite;
}

.processing-indicator {
  text-align: center;
  padding: 2rem;
  color: #718096;
}

.processing-indicator p {
  margin-top: 1rem;
  font-weight: 500;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes wave {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@media (max-width: 768px) {
  .speech-interface {
    padding: 1rem;
  }
  
  .speech-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .recording-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .record-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .response-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
