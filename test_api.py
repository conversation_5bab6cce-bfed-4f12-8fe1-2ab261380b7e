#!/usr/bin/env python3
"""
Simple API test script to verify the LLM Wrapper backend functionality.
Run this script to test the main API endpoints.
"""

import requests
import json
import sys
import time

API_BASE_URL = "http://localhost:8080/api"

def test_endpoint(method, endpoint, data=None, description=""):
    """Test a single API endpoint"""
    url = f"{API_BASE_URL}{endpoint}"
    print(f"\n{'='*60}")
    print(f"Testing: {method} {endpoint}")
    print(f"Description: {description}")
    print(f"URL: {url}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=30)
        else:
            print(f"Unsupported method: {method}")
            return False
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                json_response = response.json()
                print("Response (JSON):")
                print(json.dumps(json_response, indent=2)[:500] + "..." if len(str(json_response)) > 500 else json.dumps(json_response, indent=2))
                return True
            except json.JSONDecodeError:
                print("Response (Text):")
                print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
                return True
        else:
            print("Error Response:")
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the Django server is running on port 8080")
        return False
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def main():
    """Run all API tests"""
    print("🚀 LLM Wrapper API Test Suite")
    print("="*60)
    
    tests = [
        {
            "method": "GET",
            "endpoint": "/chat/domains/",
            "description": "Get available content domains"
        },
        {
            "method": "GET", 
            "endpoint": "/chat/health/",
            "description": "Health check endpoint"
        },
        {
            "method": "POST",
            "endpoint": "/chat/",
            "data": {
                "message": "Hello, can you provide some guidance?",
                "interaction_type": "general",
                "session_id": "test-session-123"
            },
            "description": "Send a chat message"
        },
        {
            "method": "POST",
            "endpoint": "/chat/daily-guidance/",
            "data": {
                "session_id": "test-session-123"
            },
            "description": "Get daily guidance"
        },
        {
            "method": "GET",
            "endpoint": "/speech/status/",
            "description": "Check speech provider status"
        }
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        success = test_endpoint(
            test["method"],
            test["endpoint"], 
            test.get("data"),
            test["description"]
        )
        if success:
            passed += 1
            print("✅ PASSED")
        else:
            print("❌ FAILED")
        
        time.sleep(1)  # Brief pause between tests
    
    print(f"\n{'='*60}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the Django server logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
