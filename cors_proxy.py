#!/usr/bin/env python3
"""
Simple CORS Proxy Server to solve the CORS issue definitively
This proxy will forward requests from the frontend to the Django backend
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import requests
import json
import urllib.parse

class CORSProxyHandler(BaseHTTPRequestHandler):
    
    def do_OPTIONS(self):
        """Handle preflight OPTIONS requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')
        self.send_header('Content-Length', '0')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests and proxy them to Django"""
        try:
            # Read the request body
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            # Parse the path to determine the Django endpoint
            django_url = f"http://localhost:8080{self.path}"
            
            # Forward the request to Django
            headers = {
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                django_url,
                data=post_data,
                headers=headers,
                timeout=30
            )
            
            # Send response back to frontend with CORS headers
            self.send_response(response.status_code)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            self.end_headers()
            
            # Forward the response body
            self.wfile.write(response.content)
            
        except Exception as e:
            # Send error response with CORS headers
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            error_response = {
                'error': f'Proxy error: {str(e)}',
                'success': False
            }
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def do_GET(self):
        """Handle GET requests and proxy them to Django"""
        try:
            django_url = f"http://localhost:8080{self.path}"
            
            response = requests.get(django_url, timeout=30)
            
            # Send response back to frontend with CORS headers
            self.send_response(response.status_code)
            self.send_header('Content-Type', response.headers.get('Content-Type', 'application/json'))
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            self.end_headers()
            
            self.wfile.write(response.content)
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            error_response = {
                'error': f'Proxy error: {str(e)}',
                'success': False
            }
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def log_message(self, format, *args):
        """Custom logging"""
        print(f"CORS Proxy: {format % args}")

if __name__ == '__main__':
    proxy_port = 8081
    server = HTTPServer(('localhost', proxy_port), CORSProxyHandler)
    print(f"🚀 CORS Proxy Server running on http://localhost:{proxy_port}")
    print(f"   Proxying requests to Django backend at http://localhost:8080")
    print(f"   Frontend should use http://localhost:{proxy_port}/api/... for API calls")
    print("   This will solve the CORS issue completely!")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 CORS Proxy Server stopped")
        server.shutdown()
