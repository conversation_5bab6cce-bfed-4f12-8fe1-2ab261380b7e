#!/usr/bin/env python3
"""
Deployment Status Checker for AI-Powered Wisdom
This script checks if your deployment is working correctly
"""

import requests
import json
import sys
from urllib.parse import urljoin

def check_backend_health(backend_url):
    """Check if backend is responding"""
    try:
        health_url = urljoin(backend_url, '/api/chat/health/')
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data.get('status', 'Unknown')}")
            return True
        else:
            print(f"❌ Backend Health Check Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Connection Failed: {str(e)}")
        return False

def check_domains_endpoint(backend_url):
    """Check if domains endpoint is working"""
    try:
        domains_url = urljoin(backend_url, '/api/domains/')
        response = requests.get(domains_url, timeout=10)
        
        if response.status_code == 200:
            domains = response.json()
            print(f"✅ Domains Endpoint: Found {len(domains)} domains")
            for domain in domains:
                print(f"   - {domain.get('name', 'Unknown')} ({domain.get('slug', 'no-slug')})")
            return True
        else:
            print(f"❌ Domains Endpoint Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Domains Endpoint Error: {str(e)}")
        return False

def check_chat_endpoint(backend_url):
    """Check if chat endpoint is working"""
    try:
        chat_url = urljoin(backend_url, '/api/chat/')
        test_data = {
            'message': 'Hello, this is a test message',
            'interaction_type': 'general',
            'domain_slug': 'biblical_texts'
        }
        
        response = requests.post(
            chat_url, 
            json=test_data, 
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat Endpoint: Working")
            print(f"   Response length: {len(data.get('response', ''))} characters")
            return True
        else:
            print(f"❌ Chat Endpoint Failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat Endpoint Error: {str(e)}")
        return False

def check_cors(backend_url, frontend_url):
    """Check CORS configuration"""
    try:
        # Make a preflight request
        response = requests.options(
            urljoin(backend_url, '/api/chat/'),
            headers={
                'Origin': frontend_url,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=10
        )
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        if cors_headers['Access-Control-Allow-Origin']:
            print(f"✅ CORS Configuration: Working")
            print(f"   Allowed Origin: {cors_headers['Access-Control-Allow-Origin']}")
            return True
        else:
            print(f"❌ CORS Configuration: Missing headers")
            return False
    except Exception as e:
        print(f"❌ CORS Check Error: {str(e)}")
        return False

def main():
    print("🔍 AI-Powered Wisdom Deployment Checker")
    print("=" * 50)
    
    # Get URLs from user
    backend_url = input("Enter your backend URL (e.g., https://your-app.railway.app): ").strip()
    if not backend_url:
        print("❌ Backend URL is required")
        sys.exit(1)
    
    frontend_url = input("Enter your frontend URL (e.g., https://your-app.vercel.app): ").strip()
    if not frontend_url:
        print("❌ Frontend URL is required")
        sys.exit(1)
    
    print(f"\n🔍 Checking deployment status...")
    print(f"Backend: {backend_url}")
    print(f"Frontend: {frontend_url}")
    print("-" * 50)
    
    # Run checks
    checks = [
        ("Backend Health", lambda: check_backend_health(backend_url)),
        ("Domains Endpoint", lambda: check_domains_endpoint(backend_url)),
        ("Chat Endpoint", lambda: check_chat_endpoint(backend_url)),
        ("CORS Configuration", lambda: check_cors(backend_url, frontend_url))
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}:")
        if check_func():
            passed += 1
        print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Summary: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Your deployment is working correctly.")
        print(f"🌐 Your app should be accessible at: {frontend_url}")
    else:
        print("⚠️ Some checks failed. Please review the errors above.")
        print("📖 Check VERCEL_DEPLOYMENT.md for troubleshooting tips.")
    
    print("\n🔗 Quick Links:")
    print(f"Frontend: {frontend_url}")
    print(f"Backend Health: {urljoin(backend_url, '/api/chat/health/')}")
    print(f"Backend Domains: {urljoin(backend_url, '/api/domains/')}")

if __name__ == "__main__":
    main()
