import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import './Header.css'

const Header = ({ activeDomain }) => {
  return (
    <header className="header">
      <div className="header-background">
        <div className="header-pattern"></div>
        <div className="header-glow"></div>
      </div>
      <div className="header-content">
        <div className="logo-section">
          <div className="logo-icon">
            <BookOpen size={36} />
            <div className="logo-accent">
              <Heart size={16} />
            </div>
          </div>
          <div className="logo-text">
            <h1>Divine Guidance</h1>
            <p className="logo-subtitle">AI-Powered Biblical Wisdom</p>
          </div>
        </div>

        <div className="header-verse">
          <Star size={16} className="verse-star" />
          <span className="verse-text">"Your word is a lamp for my feet, a light on my path"</span>
          <span className="verse-reference">Psalm 119:105</span>
          <Star size={16} className="verse-star" />
        </div>

        <div className="domain-info">
          <Cross size={18} />
          <span className="domain-name">
            {activeDomain ? activeDomain.name : 'Biblical Guidance'}
          </span>
        </div>
      </div>
    </header>
  )
}

export default Header
