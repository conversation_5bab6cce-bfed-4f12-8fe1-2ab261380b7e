import { Brain, Globe } from 'lucide-react'
import './Header.css'

const Header = ({ activeDomain }) => {
  return (
    <header className="header">
      <div className="header-content">
        <div className="logo-section">
          <Brain className="logo-icon" size={32} />
          <div className="logo-text">
            <h1>LLM Wrapper</h1>
            <p>Modular AI Guidance Platform</p>
          </div>
        </div>
        
        <div className="domain-info">
          <Globe size={20} />
          <span className="domain-name">
            {activeDomain ? activeDomain.name : 'Loading...'}
          </span>
        </div>
      </div>
    </header>
  )
}

export default Header
