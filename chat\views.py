"""
Chat API Views

Provides REST API endpoints for chat functionality.
"""

import time
import uuid
import logging
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpResponse
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.views import APIView

from llm_integration.services import llm_service
from content_management.services import ContentService
from content_management.models import InteractionLog, ContentDomain
from .serializers import (
    ChatMessageSerializer, ChatResponseSerializer,
    DailyGuidanceSerializer, DailyGuidanceResponseSerializer,
    InteractionLogSerializer, ContentDomainSerializer,
    DomainSwitchSerializer, ChatHistorySerializer,
    ChatHistoryResponseSerializer
)

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class ChatView(APIView):
    """Main chat endpoint for AI interactions"""

    def options(self, request, *args, **kwargs):
        """Handle preflight OPTIONS requests"""
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, Accept, Origin'
        response['Access-Control-Max-Age'] = '86400'
        response.status_code = 200
        return response

    def post(self, request):
        """Handle chat messages"""
        serializer = ChatMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        message = data['message']
        interaction_type = data['interaction_type']
        session_id = data.get('session_id') or str(uuid.uuid4())
        context_override = data.get('context_override', '')
        domain_slug = data.get('domain_slug', 'biblical_texts')

        start_time = time.time()

        try:
            # Create fresh content service instance and switch domain if specified
            content_service = ContentService()
            if domain_slug:
                logger.info(f"Switching to domain: {domain_slug}")
                success = content_service.switch_domain(domain_slug)
                logger.info(f"Domain switch result: {success}")

            # Get context from content management system
            if context_override:
                context = context_override
                logger.info("Using context override")
            else:
                context = content_service.get_context_for_interaction(message, interaction_type)
                logger.info(f"Retrieved context length: {len(context)} characters")

            # Generate AI response
            llm_response = llm_service.generate_response(
                prompt=message,
                context=context,
                interaction_type=interaction_type,
                domain_slug=domain_slug
            )

            response_time = time.time() - start_time

            # Log the interaction
            self._log_interaction(
                session_id=session_id,
                interaction_type=interaction_type,
                user_input=message,
                ai_response=llm_response.get('response', ''),
                context_used=context,
                response_time=response_time,
                success=llm_response.get('success', False),
                error_message=llm_response.get('error', '')
            )

            # Prepare response
            response_data = {
                'response': llm_response.get('response', ''),
                'interaction_type': interaction_type,
                'session_id': session_id,
                'success': llm_response.get('success', False),
                'response_time': response_time,
                'model_info': {
                    'provider': llm_response.get('provider', ''),
                    'model': llm_response.get('model', '')
                }
            }

            if not llm_response.get('success', False):
                response_data['error'] = llm_response.get('error', 'Unknown error')

            if context:
                response_data['context_used'] = context[:200] + '...' if len(context) > 200 else context

            response_serializer = ChatResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                response = Response(response_serializer.data, status=status.HTTP_200_OK)
                response['Access-Control-Allow-Origin'] = '*'
                response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
                response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, Accept, Origin'
                return response
            else:
                response = Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                response['Access-Control-Allow-Origin'] = '*'
                return response

        except Exception as e:
            logger.error(f"Chat error: {e}")
            response_time = time.time() - start_time

            # Log the error
            self._log_interaction(
                session_id=session_id,
                interaction_type=interaction_type,
                user_input=message,
                ai_response='',
                context_used='',
                response_time=response_time,
                success=False,
                error_message=str(e)
            )

            return Response({
                'response': 'I apologize, but I encountered an error while processing your request.',
                'interaction_type': interaction_type,
                'session_id': session_id,
                'success': False,
                'error': str(e),
                'response_time': response_time
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _log_interaction(self, **kwargs):
        """Log interaction to database"""
        try:
            # Get current domain
            content_service = ContentService()
            current_domain = content_service.active_domain
            domain_name = current_domain.name if current_domain else 'unknown'

            InteractionLog.objects.create(
                content_domain=domain_name,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Failed to log interaction: {e}")


class DailyGuidanceView(APIView):
    """Endpoint for daily guidance requests"""

    def post(self, request):
        """Get daily guidance"""
        serializer = DailyGuidanceSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        session_id = data.get('session_id') or str(uuid.uuid4())
        custom_request = data.get('custom_request', '')
        domain_slug = data.get('domain_slug', 'biblical_texts')

        try:
            # Get daily content
            content_service = ContentService()
            if domain_slug:
                content_service.switch_domain(domain_slug)
            daily_content = content_service.get_daily_guidance_content()

            # Build prompt for daily guidance
            if custom_request:
                prompt = f"Please provide daily guidance related to: {custom_request}"
            else:
                prompt = "Please provide daily spiritual guidance and inspiration for today."

            # Add content context if available
            context = ""
            content_piece_data = None
            if daily_content:
                context = f"**{daily_content.title}**"
                if daily_content.reference:
                    context += f" ({daily_content.reference})"
                context += f"\n{daily_content.content}"

                content_piece_data = {
                    'title': daily_content.title,
                    'content': daily_content.content,
                    'reference': daily_content.reference,
                    'content_type': daily_content.content_type
                }

            # Generate AI response
            llm_response = llm_service.generate_response(
                prompt=prompt,
                context=context,
                interaction_type='daily_guidance',
                domain_slug=domain_slug
            )

            response_data = {
                'guidance': llm_response.get('response', ''),
                'session_id': session_id,
                'success': llm_response.get('success', False)
            }

            if content_piece_data:
                response_data['content_piece'] = content_piece_data

            if not llm_response.get('success', False):
                response_data['error'] = llm_response.get('error', 'Unknown error')

            response_serializer = DailyGuidanceResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Daily guidance error: {e}")
            return Response({
                'guidance': 'I apologize, but I encountered an error while preparing your daily guidance.',
                'session_id': session_id,
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ContentDomainView(APIView):
    """Endpoint for managing content domains"""

    def get(self, request):
        """Get available content domains"""
        content_service = ContentService()
        domains = content_service.get_available_domains()
        serializer = ContentDomainSerializer(domains, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """Switch content domain"""
        serializer = DomainSwitchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        domain_slug = serializer.validated_data['domain_slug']

        content_service = ContentService()
        if content_service.switch_domain(domain_slug):
            return Response({
                'success': True,
                'message': f'Switched to domain: {domain_slug}',
                'active_domain': domain_slug
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': f'Domain not found: {domain_slug}'
            }, status=status.HTTP_404_NOT_FOUND)


class ChatHistoryView(APIView):
    """Endpoint for retrieving chat history"""

    def post(self, request):
        """Get chat history for a session"""
        serializer = ChatHistorySerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        session_id = data['session_id']
        limit = data['limit']

        try:
            interactions = InteractionLog.objects.filter(
                session_id=session_id
            ).order_by('-created_at')[:limit]

            total_count = InteractionLog.objects.filter(session_id=session_id).count()

            response_data = {
                'interactions': InteractionLogSerializer(interactions, many=True).data,
                'total_count': total_count,
                'session_id': session_id
            }

            response_serializer = ChatHistoryResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Chat history error: {e}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def health_check(request):
    """Health check endpoint"""
    try:
        # Check LLM service status
        llm_status = llm_service.get_provider_status()

        # Check content service status
        content_service = ContentService()
        content_status = {
            'active_domain': content_service.active_domain.name if content_service.active_domain else None,
            'provider_available': content_service.provider is not None
        }

        return Response({
            'status': 'healthy',
            'llm_providers': llm_status,
            'content_service': content_status,
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def load_content(request):
    """Load sample content into database - for production setup"""
    try:
        from django.core.management import call_command
        import io

        # Capture command output
        output = io.StringIO()

        # Load all content domains
        domains_loaded = []

        # Biblical texts
        try:
            call_command('load_sample_content',
                        domain='biblical_texts',
                        file='content/domains/biblical_texts/sample_content.json',
                        stdout=output)
            domains_loaded.append('biblical_texts')
        except Exception as e:
            logger.error(f"Failed to load biblical_texts: {str(e)}")

        # Buddhist teachings
        try:
            call_command('load_sample_content',
                        domain='buddhist_teachings',
                        file='content/domains/buddhist_teachings/sample_content.json',
                        stdout=output)
            domains_loaded.append('buddhist_teachings')
        except Exception as e:
            logger.error(f"Failed to load buddhist_teachings: {str(e)}")

        # Self-help
        try:
            call_command('load_sample_content',
                        domain='self_help',
                        file='content/domains/self_help/sample_content.json',
                        stdout=output)
            domains_loaded.append('self_help')
        except Exception as e:
            logger.error(f"Failed to load self_help: {str(e)}")

        # Get final status
        from content_management.models import ContentDomain, ContentPiece
        total_domains = ContentDomain.objects.count()
        total_content = ContentPiece.objects.count()

        return Response({
            'status': 'success',
            'domains_loaded': domains_loaded,
            'total_domains': total_domains,
            'total_content_pieces': total_content,
            'output': output.getvalue(),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Content loading error: {str(e)}")
        return Response({
            'status': 'error',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
