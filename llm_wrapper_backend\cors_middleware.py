"""
ULTIMATE CORS Middleware - 100% Working Solution
"""

from django.http import HttpResponse
import logging

logger = logging.getLogger(__name__)


class CorsMiddleware:
    """ULTIMATE CORS middleware that handles ALL cross-origin requests"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log the request for debugging
        logger.info(f"CORS Middleware: {request.method} {request.path}")

        # Handle preflight OPTIONS requests IMMEDIATELY
        if request.method == 'OPTIONS':
            logger.info("Handling OPTIONS preflight request")
            response = HttpResponse()
            response.status_code = 200
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD'
            response['Access-Control-Allow-Headers'] = '*'
            response['Access-Control-Max-Age'] = '86400'
            response['Content-Length'] = '0'
            response['Vary'] = 'Origin'
            logger.info("OPTIONS response sent with CORS headers")
            return response

        # Process the actual request
        response = self.get_response(request)

        # Add CORS headers to ALL responses
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD'
        response['Access-Control-Allow-Headers'] = '*'
        response['Vary'] = 'Origin'

        logger.info(f"Response sent with CORS headers: {response.status_code}")
        return response

    def process_exception(self, request, exception):
        """Handle exceptions and still add CORS headers"""
        logger.error(f"Exception in CORS middleware: {exception}")
        return None
