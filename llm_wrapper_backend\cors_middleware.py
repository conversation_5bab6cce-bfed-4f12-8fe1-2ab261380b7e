"""
Custom CORS Middleware to handle cross-origin requests - DEFINITIVE VERSION
"""

from django.http import HttpResponse


class CorsMiddleware:
    """Custom CORS middleware that DEFINITELY works"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Handle preflight OPTIONS requests for ALL paths
        if request.method == 'OPTIONS':
            response = HttpResponse()
            response.status_code = 200
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, Origin, Accept, Cache-Control, Pragma'
            response['Access-Control-Max-Age'] = '86400'
            response['Content-Length'] = '0'
            return response

        # Process the actual request
        response = self.get_response(request)

        # Add CORS headers to ALL responses
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, Origin, Accept, Cache-Control, Pragma'

        return response
