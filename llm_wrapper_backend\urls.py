"""
URL configuration for llm_wrapper_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# Import views directly to fix URL routing issue
from chat import views as chat_views
from speech import views as speech_views

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

@csrf_exempt
@require_http_methods(["GET", "POST", "OPTIONS"])
def test_cors(request):
    """Simple test endpoint for CORS"""
    response = JsonResponse({'message': 'CORS test successful', 'method': request.method})
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type'
    return response

urlpatterns = [
    path('admin/', admin.site.urls),
    # Test endpoint
    path('api/test/', test_cors, name='test_cors'),
    # Chat endpoints
    path('api/chat/', chat_views.ChatView.as_view(), name='chat'),
    path('api/chat/daily-guidance/', chat_views.DailyGuidanceView.as_view(), name='daily_guidance'),
    path('api/chat/domains/', chat_views.ContentDomainView.as_view(), name='content_domains'),
    path('api/chat/history/', chat_views.ChatHistoryView.as_view(), name='chat_history'),
    path('api/chat/health/', chat_views.health_check, name='health_check'),
    # Domain management endpoints
    path('api/domains/', chat_views.ContentDomainView.as_view(), name='domains_list'),
    path('api/domains/switch/', chat_views.ContentDomainView.as_view(), name='domains_switch'),
    # Speech endpoints
    path('api/speech/stt/', speech_views.SpeechToTextView.as_view(), name='speech_to_text'),
    path('api/speech/simple-stt/', speech_views.simple_speech_to_text, name='simple_speech_to_text'),
    path('api/speech/tts/', speech_views.TextToSpeechView.as_view(), name='text_to_speech'),
    path('api/speech/chat/', speech_views.SpeechChatView.as_view(), name='speech_chat'),
    path('api/speech/status/', speech_views.SpeechProviderStatusView.as_view(), name='speech_status'),
    path('api/speech/test/', speech_views.speech_test, name='speech_test'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
