import { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import SpeechInterface from './components/SpeechInterface'
import DomainSelector from './components/DomainSelector'
import Header from './components/Header'
import { MessageSquare, Mic, Settings } from 'lucide-react'
import './App.css'

function App() {
  const [activeInterface, setActiveInterface] = useState('chat')
  const [sessionId, setSessionId] = useState('')
  const [activeDomain, setActiveDomain] = useState(null)

  useEffect(() => {
    // Generate session ID
    setSessionId(crypto.randomUUID())
  }, [])

  const handleDomainChange = (domain) => {
    setActiveDomain(domain)
  }

  return (
    <div className="app">
      <Header activeDomain={activeDomain} />

      <div className="app-content">
        <div className="sidebar">
          <div className="interface-selector">
            <button
              className={`interface-btn ${activeInterface === 'chat' ? 'active' : ''}`}
              onClick={() => setActiveInterface('chat')}
            >
              <MessageSquare size={20} />
              Chat
            </button>
            <button
              className={`interface-btn ${activeInterface === 'speech' ? 'active' : ''}`}
              onClick={() => setActiveInterface('speech')}
            >
              <Mic size={20} />
              Speech
            </button>
            <button
              className={`interface-btn ${activeInterface === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveInterface('settings')}
            >
              <Settings size={20} />
              Settings
            </button>
          </div>

          {activeInterface === 'settings' && (
            <DomainSelector onDomainChange={handleDomainChange} />
          )}
        </div>

        <div className="main-content">
          {activeInterface === 'chat' && (
            <ChatInterface sessionId={sessionId} />
          )}
          {activeInterface === 'speech' && (
            <SpeechInterface sessionId={sessionId} />
          )}
          {activeInterface === 'settings' && (
            <div className="settings-panel">
              <h2>Settings</h2>
              <p>Configure your LLM Wrapper application settings here.</p>
              <DomainSelector onDomainChange={handleDomainChange} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
