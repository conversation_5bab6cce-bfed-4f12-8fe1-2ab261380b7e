import { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import SpeechInterface from './components/SpeechInterface'
import DomainSelector from './components/DomainSelector'
import Header from './components/Header'
import { MessageSquare, Mic, Settings, BookOpen, Heart, Sparkles } from 'lucide-react'
import './App.css'

function App() {
  const [activeInterface, setActiveInterface] = useState('chat')
  const [sessionId, setSessionId] = useState('')
  const [activeDomain, setActiveDomain] = useState(null)

  useEffect(() => {
    // Generate session ID
    setSessionId(crypto.randomUUID())

    // Initialize with default domain
    setActiveDomain({
      id: 1,
      name: 'Biblical Texts',
      slug: 'biblical_texts',
      description: 'Biblical wisdom, verses, and teachings for spiritual guidance and interpretation',
      is_active: true
    })
  }, [])

  const handleDomainChange = async (domain) => {
    console.log('Domain changed to:', domain)
    setActiveDomain(domain)

    // Communicate domain change to backend
    try {
      const response = await fetch('http://localhost:8081/api/domains/switch/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain_slug: domain.slug
        })
      })

      if (response.ok) {
        console.log('Backend domain switched successfully')
      } else {
        console.warn('Backend domain switch failed, but continuing with frontend state')
      }
    } catch (error) {
      console.warn('Could not communicate domain change to backend:', error)
    }
  }

  return (
    <div className="app">
      <Header activeDomain={activeDomain} />

      <div className="app-content">
        <div className="sidebar">
          <div className="interface-selector">
            <h3>
              <BookOpen size={20} />
              Choose Your Path
            </h3>
            <button
              className={`interface-btn ${activeInterface === 'chat' ? 'active' : ''}`}
              onClick={() => setActiveInterface('chat')}
            >
              <div className="btn-icon">
                <MessageSquare size={22} />
              </div>
              <div className="btn-content">
                <div className="btn-title">Text Guidance</div>
                <div className="btn-subtitle">Written biblical wisdom</div>
              </div>
            </button>
            <button
              className={`interface-btn ${activeInterface === 'speech' ? 'active' : ''}`}
              onClick={() => setActiveInterface('speech')}
            >
              <div className="btn-icon">
                <Mic size={22} />
              </div>
              <div className="btn-content">
                <div className="btn-title">Voice Guidance</div>
                <div className="btn-subtitle">Speak and listen to wisdom</div>
              </div>
            </button>
            <button
              className={`interface-btn ${activeInterface === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveInterface('settings')}
            >
              <div className="btn-icon">
                <Settings size={22} />
              </div>
              <div className="btn-content">
                <div className="btn-title">Settings</div>
                <div className="btn-subtitle">Customize your experience</div>
              </div>
            </button>
          </div>

          {activeInterface === 'settings' && (
            <DomainSelector onDomainChange={handleDomainChange} />
          )}
        </div>

        <div className="main-content">
          {activeInterface === 'chat' && (
            <ChatInterface sessionId={sessionId} activeDomain={activeDomain} />
          )}
          {activeInterface === 'speech' && (
            <SpeechInterface sessionId={sessionId} activeDomain={activeDomain} />
          )}
          {activeInterface === 'settings' && (
            <div className="settings-panel">
              <h2>Settings</h2>
              <p>Configure your LLM Wrapper application settings here.</p>
              <DomainSelector onDomainChange={handleDomainChange} activeDomain={activeDomain} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
