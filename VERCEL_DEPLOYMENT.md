# 🚀 Vercel Deployment Guide - AI-Powered Wisdom

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Push your code to GitHub
3. **Backend Deployment**: Deploy Django backend separately (see Backend Options below)

## 🎯 Deployment Strategy

### Frontend: Vercel (React App)
### Backend: External Service (Django API)

## 📦 Step 1: Prepare Your Repository

### 1.1 Commit All Changes
```bash
git add .
git commit -m "Prepare for Vercel deployment"
git push origin main
```

### 1.2 Verify File Structure
```
your-repo/
├── vercel.json                 # ✅ Created
├── frontend/
│   ├── .env.example           # ✅ Created
│   ├── src/config.js          # ✅ Created
│   └── package.json           # ✅ Updated
└── (Django backend files)
```

## 🌐 Step 2: Deploy Backend First

### Option A: Railway (Recommended)
1. Go to [railway.app](https://railway.app)
2. Connect your GitHub repository
3. Select "Deploy from GitHub repo"
4. Choose your repository
5. Railway will auto-detect Django
6. Add environment variables:
   ```
   GEMINI_API_KEY=your_gemini_key
   ASSEMBLYAI_API_KEY=your_assemblyai_key
   SECRET_KEY=your_secret_key
   DEBUG=False
   ALLOWED_HOSTS=your-app.railway.app
   ```
7. Deploy and note your backend URL

### Option B: Render
1. Go to [render.com](https://render.com)
2. Create new "Web Service"
3. Connect GitHub repository
4. Configure:
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `gunicorn llm_wrapper_backend.wsgi:application`
5. Add environment variables (same as above)

### Option C: Heroku
1. Install Heroku CLI
2. Create Heroku app: `heroku create your-app-name`
3. Add buildpack: `heroku buildpacks:set heroku/python`
4. Set environment variables: `heroku config:set GEMINI_API_KEY=your_key`
5. Deploy: `git push heroku main`

## 🎨 Step 3: Deploy Frontend to Vercel

### 3.1 Via Vercel Dashboard (Recommended)

1. **Go to Vercel Dashboard**
   - Visit [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"

2. **Import Repository**
   - Select "Import Git Repository"
   - Choose your GitHub repository
   - Click "Import"

3. **Configure Project**
   - **Framework Preset**: Vite
   - **Root Directory**: `frontend`
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

4. **Environment Variables**
   - Add these environment variables in Vercel:
   ```
   VITE_API_BASE_URL=https://your-backend-url.railway.app/api
   VITE_WS_BASE_URL=wss://your-backend-url.railway.app
   VITE_APP_NAME=AI-Powered Wisdom
   VITE_ENABLE_SPEECH=true
   ```

5. **Deploy**
   - Click "Deploy"
   - Wait for build to complete
   - Your app will be live at `https://your-app.vercel.app`

### 3.2 Via Vercel CLI (Alternative)

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy from project root
vercel

# Follow prompts:
# - Set up and deploy? Y
# - Which scope? (your account)
# - Link to existing project? N
# - Project name: ai-powered-wisdom
# - In which directory is your code located? ./frontend

# For production deployment
vercel --prod
```

## ⚙️ Step 4: Configure Environment Variables

### 4.1 In Vercel Dashboard
1. Go to your project in Vercel dashboard
2. Click "Settings" → "Environment Variables"
3. Add these variables:

| Name | Value | Environment |
|------|-------|-------------|
| `VITE_API_BASE_URL` | `https://your-backend.railway.app/api` | Production |
| `VITE_WS_BASE_URL` | `wss://your-backend.railway.app` | Production |
| `VITE_APP_NAME` | `AI-Powered Wisdom` | All |
| `VITE_ENABLE_SPEECH` | `true` | All |

### 4.2 Redeploy After Adding Variables
- Go to "Deployments" tab
- Click "..." on latest deployment
- Click "Redeploy"

## 🔧 Step 5: Configure Backend CORS

Update your Django backend to allow your Vercel domain:

```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "https://your-app.vercel.app",
    "https://your-custom-domain.com",  # if you have one
    "http://localhost:5173",  # for development
]

ALLOWED_HOSTS = [
    'your-backend.railway.app',
    'localhost',
    '127.0.0.1',
]
```

## 🎯 Step 6: Test Your Deployment

### 6.1 Basic Functionality Test
1. Visit your Vercel URL
2. Check if the app loads without errors
3. Open browser console - should see environment logs
4. Try switching domains in Settings
5. Test chat functionality
6. Test speech functionality (if enabled)

### 6.2 API Connectivity Test
```javascript
// Open browser console on your Vercel app
fetch('https://your-backend.railway.app/api/chat/health/')
  .then(r => r.json())
  .then(console.log)
```

## 🌟 Step 7: Custom Domain (Optional)

### 7.1 Add Custom Domain in Vercel
1. Go to project settings
2. Click "Domains"
3. Add your domain
4. Follow DNS configuration instructions

### 7.2 Update Backend CORS
Add your custom domain to `CORS_ALLOWED_ORIGINS`

## 🔍 Troubleshooting

### Common Issues:

#### 1. Build Fails
- **Check build logs** in Vercel dashboard
- **Verify package.json** has correct scripts
- **Check for missing dependencies**

#### 2. API Calls Fail
- **Verify backend URL** in environment variables
- **Check CORS configuration** in Django
- **Test backend health endpoint** directly

#### 3. Environment Variables Not Working
- **Redeploy after adding** environment variables
- **Check variable names** start with `VITE_`
- **Verify in browser console** config logs

#### 4. Speech Interface Issues
- **HTTPS required** for microphone access
- **Check browser permissions**
- **Verify AssemblyAI API key** in backend

### Debug Commands:
```bash
# Check Vercel logs
vercel logs

# Local preview of production build
cd frontend
npm run build
npm run preview
```

## 📊 Monitoring & Analytics

### Vercel Analytics
1. Go to project dashboard
2. Enable "Analytics" tab
3. Monitor performance and usage

### Backend Monitoring
- Use your backend platform's monitoring tools
- Set up error tracking (Sentry, etc.)
- Monitor API response times

## 🚀 Continuous Deployment

### Automatic Deployments
- Vercel automatically deploys on git push to main branch
- Configure branch deployments for staging/preview

### Manual Deployments
```bash
# Deploy specific branch
vercel --prod --branch feature-branch

# Deploy with specific environment
vercel --prod --env VITE_API_BASE_URL=https://staging-backend.com/api
```

## ✅ Success Checklist

- [ ] Backend deployed and accessible
- [ ] Frontend deployed to Vercel
- [ ] Environment variables configured
- [ ] CORS properly configured
- [ ] Domain switching works
- [ ] Chat interface functional
- [ ] Speech interface functional (if enabled)
- [ ] No console errors
- [ ] Mobile responsive
- [ ] Custom domain configured (optional)

## 🎉 Your App is Live!

Your AI-Powered Wisdom application is now deployed and accessible worldwide!

**Frontend URL**: `https://your-app.vercel.app`
**Backend URL**: `https://your-backend.railway.app`

Share your wisdom platform with the world! 🌍✨
