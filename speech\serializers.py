"""
Speech API Serializers

Defines serializers for speech-related API endpoints.
"""

from rest_framework import serializers


class SpeechToTextSerializer(serializers.Serializer):
    """Serializer for speech-to-text requests"""
    
    audio_file = serializers.FileField()
    provider = serializers.CharField(max_length=50, required=False, allow_blank=True)
    session_id = serializers.CharField(max_length=100, required=False, allow_blank=True)


class SpeechToTextResponseSerializer(serializers.Serializer):
    """Serializer for speech-to-text responses"""
    
    text = serializers.CharField()
    success = serializers.BooleanField()
    confidence = serializers.FloatField(required=False, allow_null=True)
    provider = serializers.CharField(required=False)
    error = serializers.CharField(required=False, allow_blank=True)
    session_id = serializers.CharField()


class TextToSpeechSerializer(serializers.Serializer):
    """Serializer for text-to-speech requests"""
    
    text = serializers.Char<PERSON><PERSON>(max_length=2000)
    voice = serializers.Char<PERSON>ield(max_length=50, required=False, allow_blank=True)
    provider = serializers.CharField(max_length=50, required=False, allow_blank=True)
    session_id = serializers.CharField(max_length=100, required=False, allow_blank=True)


class TextToSpeechResponseSerializer(serializers.Serializer):
    """Serializer for text-to-speech responses"""
    
    success = serializers.BooleanField()
    audio_url = serializers.URLField(required=False, allow_null=True)
    provider = serializers.CharField(required=False)
    client_side = serializers.BooleanField(required=False)
    instructions = serializers.CharField(required=False, allow_blank=True)
    error = serializers.CharField(required=False, allow_blank=True)
    session_id = serializers.CharField()
    text = serializers.CharField(required=False)
    voice = serializers.CharField(required=False)


class SpeechChatSerializer(serializers.Serializer):
    """Serializer for speech-based chat requests"""

    audio_file = serializers.FileField()
    interaction_type = serializers.ChoiceField(
        choices=[
            ('daily_guidance', 'Daily Guidance'),
            ('interpretation', 'Interpretation'),
            ('conversational', 'Conversational'),
            ('therapeutic', 'Therapeutic'),
            ('general', 'General'),
        ],
        default='general'
    )
    session_id = serializers.CharField(max_length=100, required=False, allow_blank=True)
    stt_provider = serializers.CharField(max_length=50, required=False, allow_blank=True)
    tts_provider = serializers.CharField(max_length=50, required=False, allow_blank=True)
    voice = serializers.CharField(max_length=50, required=False, allow_blank=True)
    domain_slug = serializers.CharField(max_length=100, required=False, default='biblical_texts')


class SpeechChatResponseSerializer(serializers.Serializer):
    """Serializer for speech-based chat responses"""
    
    transcribed_text = serializers.CharField()
    ai_response = serializers.CharField()
    audio_response = serializers.DictField()  # TTS response data
    interaction_type = serializers.CharField()
    session_id = serializers.CharField()
    success = serializers.BooleanField()
    error = serializers.CharField(required=False, allow_blank=True)
    response_time = serializers.FloatField(required=False)


class SpeechProviderStatusSerializer(serializers.Serializer):
    """Serializer for speech provider status"""
    
    speech_to_text = serializers.DictField()
    text_to_speech = serializers.DictField()
