#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'llm_wrapper_backend.settings')
django.setup()

from content_management.models import ContentDomain, ContentCategory, ContentPiece
from content_management.services import ContentService

def test_content():
    print("=== TESTING CONTENT DOMAINS ===")
    
    # Check all domains
    domains = ContentDomain.objects.all()
    print(f"Total domains: {domains.count()}")
    
    for domain in domains:
        content_count = ContentPiece.objects.filter(category__domain=domain).count()
        print(f"- {domain.name} ({domain.slug}): {content_count} content pieces, Active: {domain.is_active}")
    
    print("\n=== TESTING BUDDHIST DOMAIN ===")
    
    # Test Buddhist domain specifically
    try:
        buddhist = ContentDomain.objects.get(slug='buddhist_teachings')
        print(f"Buddhist domain found: {buddhist.name}")
        print(f"Buddhist config: {buddhist.config}")
        
        categories = ContentCategory.objects.filter(domain=buddhist)
        print(f"Buddhist categories: {categories.count()}")
        
        for cat in categories:
            pieces = ContentPiece.objects.filter(category=cat)
            print(f"  - {cat.name}: {pieces.count()} pieces")
            for piece in pieces[:2]:  # Show first 2 pieces
                print(f"    * {piece.title}")
        
    except ContentDomain.DoesNotExist:
        print("Buddhist domain not found!")
    
    print("\n=== TESTING CONTENT SERVICE ===")
    
    # Test content service
    service = ContentService()
    print(f"Active domain: {service.active_domain}")
    
    # Test switching to Buddhist
    success = service.switch_domain('buddhist_teachings')
    print(f"Switch to Buddhist: {success}")
    print(f"New active domain: {service.active_domain}")
    
    # Test getting context
    context = service.get_context_for_interaction("what is non violence", "general")
    print(f"Context length: {len(context)}")
    print(f"Context preview: {context[:200]}...")

if __name__ == "__main__":
    test_content()
