"""
LLM Integration Services - Modular and Swappable LLM Providers

This module provides an abstracted interface for LLM providers.
Currently implements Gemini API, but designed for easy swapping.
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from django.conf import settings
import google.generativeai as genai

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        """Generate a response from the LLM"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class GeminiProvider(LLMProvider):
    """Google Gemini API Provider"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'GEMINI_API_KEY', None)
        self.model_name = "gemini-2.0-flash"
        self.model = None
        self._configure()
    
    def _configure(self):
        """Configure the Gemini API"""
        if self.api_key:
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                logger.info("Gemini API configured successfully")
            except Exception as e:
                logger.error(f"Failed to configure Gemini API: {e}")
                self.model = None
        else:
            logger.warning("Gemini API key not found in settings")
    
    def is_configured(self) -> bool:
        """Check if Gemini is properly configured"""
        return self.model is not None
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        """Generate response using Gemini API"""
        if not self.is_configured():
            return {
                'success': False,
                'error': 'Gemini API not configured',
                'response': 'I apologize, but the AI service is not properly configured.'
            }

        try:
            # Combine context and prompt
            full_prompt = self._build_prompt(prompt, context, **kwargs)

            # Generate response using the correct model
            response = self.model.generate_content(full_prompt)

            # Check if response was successful
            if response and hasattr(response, 'text') and response.text:
                return {
                    'success': True,
                    'response': response.text,
                    'model': self.model_name,
                    'provider': 'gemini'
                }
            else:
                return {
                    'success': False,
                    'error': 'Empty response from Gemini API',
                    'response': 'I apologize, but I received an empty response. Please try again.'
                }

        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': 'I apologize, but I encountered an error while processing your request.'
            }
    
    def _build_prompt(self, prompt: str, context: str = "", **kwargs) -> str:
        """Build the complete prompt with context and instructions"""
        interaction_type = kwargs.get('interaction_type', 'general')
        
        # Base system instructions
        system_instructions = {
            'daily_guidance': """You are a wise spiritual guide providing daily guidance and inspiration. 
                               Draw from biblical wisdom and provide practical, uplifting advice.""",
            'interpretation': """You are a biblical scholar and interpreter. Provide thoughtful, 
                               contextual interpretations of biblical passages and concepts.""",
            'conversational': """You are a compassionate conversational partner. Engage in meaningful 
                               dialogue while incorporating spiritual wisdom when appropriate.""",
            'therapeutic': """You are a supportive counselor with deep spiritual insight. Provide 
                            comfort, guidance, and perspective while being empathetic and understanding.""",
            'general': """You are a knowledgeable and wise assistant with expertise in biblical and 
                        spiritual matters. Provide helpful, thoughtful responses."""
        }
        
        instruction = system_instructions.get(interaction_type, system_instructions['general'])
        
        # Build the complete prompt
        full_prompt = f"{instruction}\n\n"
        
        if context:
            full_prompt += f"Additional Context:\n{context}\n\n"
        
        full_prompt += f"User Question: {prompt}\n\nResponse:"
        
        return full_prompt


class LLMService:
    """Main LLM service that manages providers"""
    
    def __init__(self):
        self.providers = {
            'gemini': GeminiProvider(),
            # Future providers can be added here
            # 'openai': OpenAIProvider(),
            # 'anthropic': AnthropicProvider(),
        }
        self.default_provider = 'gemini'
    
    def get_provider(self, provider_name: str = None) -> LLMProvider:
        """Get a specific provider or the default one"""
        if provider_name is None:
            provider_name = self.default_provider
        
        provider = self.providers.get(provider_name)
        if provider is None:
            raise ValueError(f"Provider '{provider_name}' not found")
        
        return provider
    
    def generate_response(self, prompt: str, context: str = "", provider: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using specified or default provider"""
        try:
            llm_provider = self.get_provider(provider)
            return llm_provider.generate_response(prompt, context, **kwargs)
        except Exception as e:
            logger.error(f"LLM Service error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': 'I apologize, but I encountered an error while processing your request.'
            }
    
    def list_available_providers(self) -> List[str]:
        """List all available providers"""
        return list(self.providers.keys())
    
    def get_provider_status(self) -> Dict[str, bool]:
        """Get configuration status of all providers"""
        return {name: provider.is_configured() for name, provider in self.providers.items()}


# Global service instance
llm_service = LLMService()
