"""
LLM Integration Services - Modular and Swappable LLM Providers

This module provides an abstracted interface for LLM providers.
Currently implements Gemini API, but designed for easy swapping.
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from django.conf import settings
import google.generativeai as genai

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        """Generate a response from the LLM"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class GeminiProvider(LLMProvider):
    """Google Gemini API Provider"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'GEMINI_API_KEY', None)
        self.model_name = "gemini-2.0-flash"
        self.model = None
        self._configure()
    
    def _configure(self):
        """Configure the Gemini API"""
        if self.api_key:
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                logger.info("Gemini API configured successfully")
            except Exception as e:
                logger.error(f"Failed to configure Gemini API: {e}")
                self.model = None
        else:
            logger.warning("Gemini API key not found in settings")
    
    def is_configured(self) -> bool:
        """Check if Gemini is properly configured"""
        return self.model is not None
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        """Generate response using Gemini API"""
        if not self.is_configured():
            return {
                'success': False,
                'error': 'Gemini API not configured',
                'response': 'I apologize, but the AI service is not properly configured.'
            }

        try:
            # Combine context and prompt
            full_prompt = self._build_prompt(prompt, context, **kwargs)

            # Generate response using the correct model
            response = self.model.generate_content(full_prompt)

            # Check if response was successful
            if response and hasattr(response, 'text') and response.text:
                return {
                    'success': True,
                    'response': response.text,
                    'model': self.model_name,
                    'provider': 'gemini'
                }
            else:
                return {
                    'success': False,
                    'error': 'Empty response from Gemini API',
                    'response': 'I apologize, but I received an empty response. Please try again.'
                }

        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': 'I apologize, but I encountered an error while processing your request.'
            }
    
    def _build_prompt(self, prompt: str, context: str = "", **kwargs) -> str:
        """Build the complete prompt with context and instructions"""
        interaction_type = kwargs.get('interaction_type', 'general')
        domain_slug = kwargs.get('domain_slug', 'biblical_texts')

        # Get domain-specific instructions from context or use defaults
        domain_instructions = self._get_domain_instructions(domain_slug, interaction_type)

        # Build the complete prompt
        full_prompt = f"{domain_instructions}\n\n"

        if context:
            full_prompt += f"Relevant Content and Context:\n{context}\n\n"

        full_prompt += f"User Question: {prompt}\n\nResponse:"

        return full_prompt

    def _get_domain_instructions(self, domain_slug: str, interaction_type: str) -> str:
        """Get domain-specific system instructions"""

        # Domain-specific instruction templates
        domain_templates = {
            'biblical_texts': {
                'daily_guidance': "You are a wise spiritual guide providing daily guidance and inspiration. Draw from biblical wisdom and provide practical, uplifting advice.",
                'interpretation': "You are a biblical scholar and interpreter. Provide thoughtful, contextual interpretations of biblical passages and concepts.",
                'conversational': "You are a compassionate conversational partner. Engage in meaningful dialogue while incorporating biblical and spiritual wisdom when appropriate.",
                'therapeutic': "You are a supportive counselor with deep spiritual insight. Provide comfort, guidance, and perspective while being empathetic and understanding.",
                'general': "You are a knowledgeable and wise assistant with expertise in biblical and spiritual matters. Provide helpful, thoughtful responses."
            },
            'buddhist_teachings': {
                'daily_guidance': "You are a wise Buddhist teacher providing daily guidance and inspiration. Draw from Buddhist wisdom, mindfulness practices, and the path to enlightenment. Focus on practical meditation and mindful living.",
                'interpretation': "You are a Buddhist scholar and teacher. Offer thoughtful interpretation of Buddhist teachings, considering the Four Noble Truths, Eightfold Path, and various Buddhist traditions.",
                'conversational': "You are a compassionate Buddhist practitioner. Engage in meaningful conversation incorporating Buddhist philosophy, mindfulness practices, and compassionate wisdom.",
                'therapeutic': "You are a supportive guide drawing from Buddhist principles. Provide comfort and guidance through mindfulness, acceptance, and the alleviation of suffering.",
                'general': "You are a knowledgeable Buddhist teacher with expertise in meditation, mindfulness, and Buddhist philosophy. Share Buddhist wisdom and teachings with focus on practical application for modern life."
            },
            'self_help': {
                'daily_guidance': "You are a motivational life coach providing practical daily guidance for personal development. Focus on actionable steps, positive mindset, and goal achievement. Emphasize self-empowerment and growth.",
                'interpretation': "You are a personal development expert. Offer practical interpretation of life challenges and opportunities. Focus on solution-oriented thinking and personal responsibility.",
                'conversational': "You are an encouraging life coach. Engage in motivational conversation that inspires action and positive change. Be encouraging and solution-focused.",
                'therapeutic': "You are a supportive life coach providing guidance for overcoming obstacles and building resilience. Focus on strength-building and positive psychology.",
                'general': "You are a knowledgeable personal development expert. Share practical wisdom and strategies for personal success and fulfillment."
            }
        }

        # Get domain-specific instructions or fall back to biblical
        domain_instructions = domain_templates.get(domain_slug, domain_templates['biblical_texts'])
        return domain_instructions.get(interaction_type, domain_instructions['general'])


class LLMService:
    """Main LLM service that manages providers"""
    
    def __init__(self):
        self.providers = {
            'gemini': GeminiProvider(),
            # Future providers can be added here
            # 'openai': OpenAIProvider(),
            # 'anthropic': AnthropicProvider(),
        }
        self.default_provider = 'gemini'
    
    def get_provider(self, provider_name: str = None) -> LLMProvider:
        """Get a specific provider or the default one"""
        if provider_name is None:
            provider_name = self.default_provider
        
        provider = self.providers.get(provider_name)
        if provider is None:
            raise ValueError(f"Provider '{provider_name}' not found")
        
        return provider
    
    def generate_response(self, prompt: str, context: str = "", provider: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using specified or default provider"""
        try:
            llm_provider = self.get_provider(provider)
            return llm_provider.generate_response(prompt, context, **kwargs)
        except Exception as e:
            logger.error(f"LLM Service error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': 'I apologize, but I encountered an error while processing your request.'
            }
    
    def list_available_providers(self) -> List[str]:
        """List all available providers"""
        return list(self.providers.keys())
    
    def get_provider_status(self) -> Dict[str, bool]:
        """Get configuration status of all providers"""
        return {name: provider.is_configured() for name, provider in self.providers.items()}


# Global service instance
llm_service = LLMService()
