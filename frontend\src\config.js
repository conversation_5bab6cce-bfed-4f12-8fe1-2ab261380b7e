// API Configuration for different environments

const config = {
  development: {
    API_BASE_URL: 'http://localhost:8081/api', // CORS proxy for development
    WS_BASE_URL: 'ws://localhost:8081'
  },
  production: {
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://your-backend-url.com/api',
    WS_BASE_URL: import.meta.env.VITE_WS_BASE_URL || 'wss://your-backend-url.com'
  }
}

// Determine environment
const environment = import.meta.env.MODE || 'development'

// Export configuration for current environment
export const API_BASE_URL = config[environment].API_BASE_URL
export const WS_BASE_URL = config[environment].WS_BASE_URL

// Export all config for debugging
export default config

console.log(`🌍 Environment: ${environment}`)
console.log(`🔗 API Base URL: ${API_BASE_URL}`)
