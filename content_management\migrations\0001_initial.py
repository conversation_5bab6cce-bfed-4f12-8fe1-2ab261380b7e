# Generated by Django 5.2.4 on 2025-07-08 14:55

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContentDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('config', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InteractionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('interaction_type', models.CharField(choices=[('daily_guidance', 'Daily Guidance'), ('interpretation', 'Interpretation'), ('conversational', 'Conversational'), ('therapeutic', 'Therapeutic'), ('general', 'General')], max_length=20)),
                ('user_input', models.TextField()),
                ('ai_response', models.TextField()),
                ('content_domain', models.CharField(max_length=100)),
                ('context_used', models.TextField(blank=True)),
                ('response_time', models.FloatField(blank=True, null=True)),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('slug', models.SlugField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='content_management.contentdomain')),
            ],
            options={
                'ordering': ['order', 'name'],
                'unique_together': {('domain', 'slug')},
            },
        ),
        migrations.CreateModel(
            name='ContentFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('file', models.FileField(upload_to='content_files/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['txt', 'json', 'csv'])])),
                ('file_type', models.CharField(choices=[('txt', 'Text File'), ('json', 'JSON File'), ('csv', 'CSV File')], max_length=10)),
                ('description', models.TextField(blank=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_files', to='content_management.contentdomain')),
            ],
        ),
        migrations.CreateModel(
            name='ContentPiece',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('content_type', models.CharField(choices=[('text', 'Text'), ('quote', 'Quote'), ('teaching', 'Teaching'), ('story', 'Story'), ('meditation', 'Meditation'), ('prayer', 'Prayer')], default='text', max_length=20)),
                ('reference', models.CharField(blank=True, max_length=100)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_pieces', to='content_management.contentcategory')),
            ],
            options={
                'ordering': ['order', 'title'],
            },
        ),
    ]
