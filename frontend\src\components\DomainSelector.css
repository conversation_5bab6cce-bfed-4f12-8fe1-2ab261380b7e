.domain-selector {
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.domain-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.domain-header h3 {
  flex: 1;
  margin: 0;
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
}

.refresh-button {
  padding: 0.25rem;
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #718096;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover:not(:disabled) {
  background: #f7fafc;
  color: #4a5568;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-state {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 2rem;
  justify-content: center;
  color: #718096;
}

.error-message {
  padding: 1rem;
  background: #fed7d7;
  color: #c53030;
  border-bottom: 1px solid #feb2b2;
  font-size: 0.875rem;
}

.empty-state {
  padding: 2rem;
  text-align: center;
  color: #718096;
}

.empty-state p {
  margin: 0 0 0.5rem 0;
}

.empty-hint {
  font-size: 0.875rem;
  margin-top: 1rem;
}

.empty-hint code {
  display: block;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  color: #2d3748;
}

.domains-list {
  max-height: 300px;
  overflow-y: auto;
}

.domain-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  transition: background 0.2s ease;
}

.domain-item:last-child {
  border-bottom: none;
}

.domain-item:hover {
  background: #f8fafc;
}

.domain-item.active {
  background: rgba(102, 126, 234, 0.05);
  border-left: 3px solid #667eea;
}

.domain-info {
  flex: 1;
}

.domain-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.active-icon {
  color: #48bb78;
}

.domain-description {
  font-size: 0.875rem;
  color: #718096;
  line-height: 1.4;
}

.switch-button {
  padding: 0.375rem 0.75rem;
  background: #667eea;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.switch-button:hover:not(:disabled) {
  background: #5a67d8;
}

.switch-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.domain-info-panel {
  padding: 1rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.domain-info-panel h4 {
  margin: 0 0 0.75rem 0;
  color: #2d3748;
  font-size: 0.875rem;
  font-weight: 600;
}

.domain-info-panel p {
  margin: 0 0 0.75rem 0;
  font-size: 0.8rem;
  color: #4a5568;
  line-height: 1.4;
}

.domain-info-panel ul {
  margin: 0 0 0.75rem 0;
  padding-left: 1rem;
  font-size: 0.8rem;
  color: #4a5568;
}

.domain-info-panel li {
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.domain-note {
  font-style: italic;
  color: #718096 !important;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .domain-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .switch-button {
    align-self: flex-end;
  }
  
  .domain-info-panel {
    font-size: 0.875rem;
  }
  
  .domain-info-panel h4 {
    font-size: 0.9rem;
  }
}
