/* Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* App Layout */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Beautiful Sidebar */
.sidebar {
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding: 2rem 1.5rem;
  overflow-y: auto;
  box-shadow: 4px 0 24px rgba(0, 0, 0, 0.05);
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 215, 0, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

.interface-selector {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.interface-selector h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.interface-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(102, 126, 234, 0.1);
  border-radius: 16px;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.interface-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.interface-btn:hover::before {
  left: 100%;
}

.interface-btn:hover {
  background: rgba(102, 126, 234, 0.08);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
}

.interface-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.interface-btn.active:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

/* Enhanced Button Styles */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.interface-btn.active .btn-icon {
  background: rgba(255, 255, 255, 0.2);
}

.interface-btn:hover .btn-icon {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.05);
}

.interface-btn.active:hover .btn-icon {
  background: rgba(255, 255, 255, 0.3);
}

.btn-content {
  flex: 1;
  text-align: left;
}

.btn-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.btn-subtitle {
  font-size: 0.85rem;
  opacity: 0.7;
  font-weight: 400;
  line-height: 1.3;
}

.interface-btn.active .btn-subtitle {
  opacity: 0.9;
}

/* Main Content Area */
.main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 24px 0 0 0;
  margin: 1rem 1rem 1rem 0;
  padding: 2rem;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 10% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  pointer-events: none;
  border-radius: 24px 0 0 0;
}

/* Settings Panel */
.settings-panel {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 4rem 2rem;
  color: #4a5568;
}

.settings-panel h2 {
  color: #2d3748;
  font-size: 2rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-panel p {
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-content {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    padding: 1rem;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .interface-selector {
    flex-direction: row;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .interface-btn {
    min-width: 120px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .main-content {
    margin: 0;
    border-radius: 0;
    padding: 1.5rem;
  }
  
  .main-content::before {
    border-radius: 0;
  }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}
