#!/usr/bin/env python3
import requests
import json

try:
    print("Testing Gemini API integration...")
    response = requests.post(
        'http://localhost:8080/api/chat/',
        json={
            'message': 'Hello, can you provide some biblical guidance?',
            'interaction_type': 'general'
        },
        timeout=20
    )
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ SUCCESS!")
        print(f"AI Response: {data.get('response', 'No response')[:200]}...")
        print(f"Success: {data.get('success', 'Unknown')}")
        if 'model_info' in data:
            print(f"Model: {data['model_info']}")
    else:
        print("❌ FAILED!")
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"❌ ERROR: {e}")
