"""
Speech API Views

Provides REST API endpoints for speech functionality.
"""

import time
import uuid
import logging
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from llm_integration.services import llm_service
from content_management.services import content_service
from .services import speech_service
from .serializers import (
    SpeechToTextSerializer, SpeechToTextResponseSerializer,
    TextToSpeechSerializer, TextToSpeechResponseSerializer,
    SpeechChatSerializer, SpeechChatResponseSerializer,
    SpeechProviderStatusSerializer
)

logger = logging.getLogger(__name__)


@csrf_exempt
def speech_test(request):
    """Simple speech test endpoint"""
    if request.method == 'OPTIONS':
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        response['Access-Control-Max-Age'] = '86400'
        return response

    if request.method == 'POST':
        response = HttpResponse()
        response['Content-Type'] = 'application/json'
        response['Access-Control-Allow-Origin'] = '*'
        response.write('{"message": "Speech test endpoint working", "success": true}')
        return response

    return HttpResponse('{"error": "Method not allowed"}', status=405)


class SpeechToTextView(APIView):
    """Endpoint for speech-to-text conversion"""

    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """Convert speech to text"""
        serializer = SpeechToTextSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        audio_file = data['audio_file']
        provider = data.get('provider')
        session_id = data.get('session_id') or str(uuid.uuid4())

        temp_file_path = None

        try:
            # Save uploaded audio file
            temp_file_path = speech_service.save_uploaded_audio(audio_file)

            # Transcribe audio
            result = speech_service.transcribe_audio(temp_file_path, provider)

            response_data = {
                'text': result.get('text', ''),
                'success': result.get('success', False),
                'session_id': session_id
            }

            if result.get('confidence'):
                response_data['confidence'] = result['confidence']

            if result.get('provider'):
                response_data['provider'] = result['provider']

            if not result.get('success', False):
                response_data['error'] = result.get('error', 'Unknown error')

            response_serializer = SpeechToTextResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Speech-to-text error: {e}")
            return Response({
                'text': '',
                'success': False,
                'error': str(e),
                'session_id': session_id
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        finally:
            # Clean up temporary file
            if temp_file_path:
                speech_service.cleanup_temp_file(temp_file_path)


class TextToSpeechView(APIView):
    """Endpoint for text-to-speech conversion"""

    def post(self, request):
        """Convert text to speech"""
        serializer = TextToSpeechSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        text = data['text']
        voice = data.get('voice')
        provider = data.get('provider')
        session_id = data.get('session_id') or str(uuid.uuid4())

        try:
            # Synthesize speech
            result = speech_service.synthesize_speech(text, voice, provider)

            response_data = {
                'success': result.get('success', False),
                'session_id': session_id
            }

            # Add all relevant fields from the result
            for field in ['audio_url', 'provider', 'client_side', 'instructions', 'text', 'voice']:
                if field in result:
                    response_data[field] = result[field]

            if not result.get('success', False):
                response_data['error'] = result.get('error', 'Unknown error')

            response_serializer = TextToSpeechResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Text-to-speech error: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'session_id': session_id
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class SpeechChatView(APIView):
    """Endpoint for complete speech-based chat interaction"""

    parser_classes = [MultiPartParser, FormParser]

    def options(self, request, *args, **kwargs):
        """Handle preflight OPTIONS requests"""
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response['Access-Control-Max-Age'] = '86400'
        response.status_code = 200
        return response

    def post(self, request):
        """Handle speech-based chat: STT -> LLM -> TTS"""
        serializer = SpeechChatSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        audio_file = data['audio_file']
        interaction_type = data['interaction_type']
        session_id = data.get('session_id') or str(uuid.uuid4())
        stt_provider = data.get('stt_provider')
        tts_provider = data.get('tts_provider')
        voice = data.get('voice')

        temp_file_path = None
        start_time = time.time()

        try:
            # Step 1: Speech to Text
            temp_file_path = speech_service.save_uploaded_audio(audio_file)
            stt_result = speech_service.transcribe_audio(temp_file_path, stt_provider)

            if not stt_result.get('success', False):
                return Response({
                    'transcribed_text': '',
                    'ai_response': '',
                    'audio_response': {},
                    'interaction_type': interaction_type,
                    'session_id': session_id,
                    'success': False,
                    'error': f"Speech transcription failed: {stt_result.get('error', 'Unknown error')}"
                }, status=status.HTTP_400_BAD_REQUEST)

            transcribed_text = stt_result.get('text', '')

            # Step 2: Get AI Response
            context = content_service.get_context_for_interaction(transcribed_text, interaction_type)
            llm_result = llm_service.generate_response(
                prompt=transcribed_text,
                context=context,
                interaction_type=interaction_type
            )

            if not llm_result.get('success', False):
                return Response({
                    'transcribed_text': transcribed_text,
                    'ai_response': '',
                    'audio_response': {},
                    'interaction_type': interaction_type,
                    'session_id': session_id,
                    'success': False,
                    'error': f"AI response failed: {llm_result.get('error', 'Unknown error')}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            ai_response = llm_result.get('response', '')

            # Step 3: Text to Speech
            tts_result = speech_service.synthesize_speech(ai_response, voice, tts_provider)

            response_time = time.time() - start_time

            response_data = {
                'transcribed_text': transcribed_text,
                'ai_response': ai_response,
                'audio_response': tts_result,
                'interaction_type': interaction_type,
                'session_id': session_id,
                'success': True,
                'response_time': response_time
            }

            response_serializer = SpeechChatResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Speech chat error: {e}")
            response_time = time.time() - start_time
            return Response({
                'transcribed_text': '',
                'ai_response': '',
                'audio_response': {},
                'interaction_type': interaction_type,
                'session_id': session_id,
                'success': False,
                'error': str(e),
                'response_time': response_time
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        finally:
            # Clean up temporary file
            if temp_file_path:
                speech_service.cleanup_temp_file(temp_file_path)


class SpeechProviderStatusView(APIView):
    """Endpoint for checking speech provider status"""

    def get(self, request):
        """Get status of all speech providers"""
        try:
            status_data = speech_service.get_provider_status()

            response_serializer = SpeechProviderStatusSerializer(data=status_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Speech provider status error: {e}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
