{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/chat.app.memberships": {"description": "On their own behalf, apps in Google Chat can see, add, update, and remove members from conversations and spaces"}, "https://www.googleapis.com/auth/chat.app.spaces": {"description": "On their own behalf, apps in Google Chat can create conversations and spaces and see or update their metadata (including history settings and access settings)"}, "https://www.googleapis.com/auth/chat.bot": {"description": "Private Service: https://www.googleapis.com/auth/chat.bot"}, "https://www.googleapis.com/auth/chat.memberships": {"description": "See, add, update, and remove members from conversations and spaces in Google Chat"}, "https://www.googleapis.com/auth/chat.memberships.readonly": {"description": "View members in Google Chat conversations."}, "https://www.googleapis.com/auth/chat.messages": {"description": "See, compose, send, update, and delete messages as well as their message content; add, see, and delete reactions to messages."}, "https://www.googleapis.com/auth/chat.messages.reactions": {"description": "See, add, and delete reactions as well as their reaction content to messages in Google Chat"}, "https://www.googleapis.com/auth/chat.messages.reactions.readonly": {"description": "View reactions as well as their reaction content to messages in Google Chat"}, "https://www.googleapis.com/auth/chat.messages.readonly": {"description": "See messages as well as their reactions and message content in Google Chat"}, "https://www.googleapis.com/auth/chat.spaces": {"description": "Create conversations and spaces and see or update metadata (including history settings and access settings) in Google Chat"}, "https://www.googleapis.com/auth/chat.spaces.readonly": {"description": "View chat and spaces in Google Chat"}, "https://www.googleapis.com/auth/drive": {"description": "See, edit, create, and delete all of your Google Drive files"}, "https://www.googleapis.com/auth/drive.file": {"description": "See, edit, create, and delete only the specific Google Drive files you use with this app"}, "https://www.googleapis.com/auth/drive.metadata": {"description": "View and manage metadata of files in your Google Drive"}, "https://www.googleapis.com/auth/drive.metadata.readonly": {"description": "See information about your Google Drive files"}, "https://www.googleapis.com/auth/drive.readonly": {"description": "See and download all your Google Drive files"}, "https://www.googleapis.com/auth/meetings.space.created": {"description": "Create, edit, and see information about your Google Meet conferences created by the app."}, "https://www.googleapis.com/auth/meetings.space.readonly": {"description": "Read information about any of your Google Meet conferences"}}}}, "basePath": "", "baseUrl": "https://workspaceevents.googleapis.com/", "batchPath": "batch", "canonicalName": "Workspace Events", "description": "The Google Workspace Events API lets you subscribe to events and manage change notifications across Google Workspace applications.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/workspace/events", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "workspaceevents:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://workspaceevents.mtls.googleapis.com/", "name": "workspaceevents", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/operations/{operationsId}", "httpMethod": "GET", "id": "workspaceevents.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/chat.bot", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}}}, "subscriptions": {"methods": {"create": {"description": "Creates a Google Workspace subscription. To learn how to use this method, see [Create a Google Workspace subscription](https://developers.google.com/workspace/events/guides/create-subscription). ", "flatPath": "v1/subscriptions", "httpMethod": "POST", "id": "workspaceevents.subscriptions.create", "parameterOrder": [], "parameters": {"validateOnly": {"description": "Optional. If set to `true`, validates and previews the request, but doesn't create the subscription.", "location": "query", "type": "boolean"}}, "path": "v1/subscriptions", "request": {"$ref": "Subscription"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/chat.app.memberships", "https://www.googleapis.com/auth/chat.app.spaces", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}, "delete": {"description": "Deletes a Google Workspace subscription. To learn how to use this method, see [Delete a Google Workspace subscription](https://developers.google.com/workspace/events/guides/delete-subscription).", "flatPath": "v1/subscriptions/{subscriptionsId}", "httpMethod": "DELETE", "id": "workspaceevents.subscriptions.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to `true` and the subscription isn't found, the request succeeds but doesn't delete the subscription.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. Etag of the subscription. If present, it must match with the server's etag. Otherwise, request fails with the status `ABORTED`.", "location": "query", "type": "string"}, "name": {"description": "Required. Resource name of the subscription to delete. Format: `subscriptions/{subscription}`", "location": "path", "pattern": "^subscriptions/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set to `true`, validates and previews the request, but doesn't delete the subscription.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/chat.bot", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}, "get": {"description": "Gets details about a Google Workspace subscription. To learn how to use this method, see [Get details about a Google Workspace subscription](https://developers.google.com/workspace/events/guides/get-subscription).", "flatPath": "v1/subscriptions/{subscriptionsId}", "httpMethod": "GET", "id": "workspaceevents.subscriptions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subscription. Format: `subscriptions/{subscription}`", "location": "path", "pattern": "^subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/chat.bot", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}, "list": {"description": "Lists Google Workspace subscriptions. To learn how to use this method, see [List Google Workspace subscriptions](https://developers.google.com/workspace/events/guides/list-subscriptions).", "flatPath": "v1/subscriptions", "httpMethod": "GET", "id": "workspaceevents.subscriptions.list", "parameterOrder": [], "parameters": {"filter": {"description": "Required. A query filter. You can filter subscriptions by event type (`event_types`) and target resource (`target_resource`). You must specify at least one event type in your query. To filter for multiple event types, use the `OR` operator. To filter by both event type and target resource, use the `AND` operator and specify the full resource name, such as `//chat.googleapis.com/spaces/{space}`. For example, the following queries are valid: ``` event_types:\"google.workspace.chat.membership.v1.updated\" OR event_types:\"google.workspace.chat.message.v1.created\" event_types:\"google.workspace.chat.message.v1.created\" AND target_resource=\"//chat.googleapis.com/spaces/{space}\" ( event_types:\"google.workspace.chat.membership.v1.updated\" OR event_types:\"google.workspace.chat.message.v1.created\" ) AND target_resource=\"//chat.googleapis.com/spaces/{space}\" ``` The server rejects invalid queries with an `INVALID_ARGUMENT` error.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of subscriptions to return. The service might return fewer than this value. If unspecified or set to `0`, up to 50 subscriptions are returned. The maximum value is 100. If you specify a value more than 100, the system only returns 100 subscriptions.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous list subscriptions call. Provide this parameter to retrieve the subsequent page. When paginating, the filter value should match the call that provided the page token. Passing a different value might lead to unexpected results.", "location": "query", "type": "string"}}, "path": "v1/subscriptions", "response": {"$ref": "ListSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/chat.bot", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}, "patch": {"description": "Updates or renews a Google Workspace subscription. To learn how to use this method, see [Update or renew a Google Workspace subscription](https://developers.google.com/workspace/events/guides/update-subscription).", "flatPath": "v1/subscriptions/{subscriptionsId}", "httpMethod": "PATCH", "id": "workspaceevents.subscriptions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Resource name of the subscription. Format: `subscriptions/{subscription}`", "location": "path", "pattern": "^subscriptions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The field to update. If omitted, updates any fields included in the request. You can update one of the following fields in a subscription: * `expire_time`: The timestamp when the subscription expires. * `ttl`: The time-to-live (TTL) or duration of the subscription. * `event_types`: The list of event types to receive about the target resource. To fully replace the subscription (the equivalent of `PUT`), use `*`. Any omitted fields are updated with empty values.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to `true`, validates and previews the request, but doesn't update the subscription.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Subscription"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/chat.app.memberships", "https://www.googleapis.com/auth/chat.app.spaces", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}, "reactivate": {"description": "Reactivates a suspended Google Workspace subscription. This method resets your subscription's `State` field to `ACTIVE`. Before you use this method, you must fix the error that suspended the subscription. This method will ignore or reject any subscription that isn't currently in a suspended state. To learn how to use this method, see [Reactivate a Google Workspace subscription](https://developers.google.com/workspace/events/guides/reactivate-subscription).", "flatPath": "v1/subscriptions/{subscriptionsId}:reactivate", "httpMethod": "POST", "id": "workspaceevents.subscriptions.reactivate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subscription. Format: `subscriptions/{subscription}`", "location": "path", "pattern": "^subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:reactivate", "request": {"$ref": "ReactivateSubscriptionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/chat.app.memberships", "https://www.googleapis.com/auth/chat.app.spaces", "https://www.googleapis.com/auth/chat.memberships", "https://www.googleapis.com/auth/chat.memberships.readonly", "https://www.googleapis.com/auth/chat.messages", "https://www.googleapis.com/auth/chat.messages.reactions", "https://www.googleapis.com/auth/chat.messages.reactions.readonly", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/chat.spaces", "https://www.googleapis.com/auth/chat.spaces.readonly", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/meetings.space.created", "https://www.googleapis.com/auth/meetings.space.readonly"]}}}}, "revision": "20250615", "rootUrl": "https://workspaceevents.googleapis.com/", "schemas": {"ListSubscriptionsResponse": {"description": "The response message for SubscriptionsService.ListSubscriptions.", "id": "ListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "subscriptions": {"description": "List of subscriptions.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "NotificationEndpoint": {"description": "The endpoint where the subscription delivers events.", "id": "NotificationEndpoint", "properties": {"pubsubTopic": {"description": "Immutable. The Pub/Sub topic that receives events for the subscription. Format: `projects/{project}/topics/{topic}` You must create the topic in the same Google Cloud project where you create this subscription. Note: The Workspace Events API uses [ordering keys](https://cloud.google.com/pubsub/docs/ordering) for the benefit of sequential events. If the Cloud Pub/Sub topic has a [message storage policy](https://cloud.google.com/pubsub/docs/resource-location-restriction#exceptions) configured to exclude the nearest Google Cloud region, publishing events with ordering keys will fail. When the topic receives events, the events are encoded as Pub/Sub messages. For details, see the [Google Cloud Pub/Sub Protocol Binding for CloudEvents](https://github.com/googleapis/google-cloudevents/blob/main/docs/spec/pubsub.md).", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "PayloadOptions": {"description": "Options about what data to include in the event payload. Only supported for Google Chat events.", "id": "PayloadOptions", "properties": {"fieldMask": {"description": "Optional. If `include_resource` is set to `true`, the list of fields to include in the event payload. Separate fields with a comma. For example, to include a Google Chat message's sender and create time, enter `message.sender,message.createTime`. If omitted, the payload includes all fields for the resource. If you specify a field that doesn't exist for the resource, the system ignores the field.", "format": "google-fieldmask", "type": "string"}, "includeResource": {"description": "Optional. Whether the event payload includes data about the resource that changed. For example, for an event where a Google Chat message was created, whether the payload contains data about the [`Message`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages) resource. If false, the event payload only includes the name of the changed resource.", "type": "boolean"}}, "type": "object"}, "ReactivateSubscriptionRequest": {"description": "The request message for SubscriptionsService.ReactivateSubscription.", "id": "ReactivateSubscriptionRequest", "properties": {}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Subscription": {"description": "A subscription to receive events about a Google Workspace resource. To learn more about subscriptions, see the [Google Workspace Events API overview](https://developers.google.com/workspace/events).", "id": "Subscription", "properties": {"authority": {"description": "Output only. The user who authorized the creation of the subscription. Format: `users/{user}` For Google Workspace users, the `{user}` value is the [`user.id`](https://developers.google.com/admin-sdk/directory/reference/rest/v1/users#User.FIELDS.ids) field from the Directory API.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time when the subscription is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and might be sent on update requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "eventTypes": {"description": "Required. Unordered list. Input for creating a subscription. Otherwise, output only. One or more types of events to receive about the target resource. Formatted according to the CloudEvents specification. The supported event types depend on the target resource of your subscription. For details, see [Supported Google Workspace events](https://developers.google.com/workspace/events/guides#supported-events). By default, you also receive events about the [lifecycle of your subscription](https://developers.google.com/workspace/events/guides/events-lifecycle). You don't need to specify lifecycle events for this field. If you specify an event type that doesn't exist for the target resource, the request returns an HTTP `400 Bad Request` status code.", "items": {"type": "string"}, "type": "array"}, "expireTime": {"description": "Non-empty default. The timestamp in UTC when the subscription expires. Always displayed on output, regardless of what was used on input.", "format": "google-datetime", "type": "string"}, "name": {"description": "Identifier. Resource name of the subscription. Format: `subscriptions/{subscription}`", "type": "string"}, "notificationEndpoint": {"$ref": "NotificationEndpoint", "description": "Required. Immutable. The endpoint where the subscription delivers events, such as a Pub/Sub topic."}, "payloadOptions": {"$ref": "PayloadOptions", "description": "Optional. Options about what data to include in the event payload. Only supported for Google Chat events."}, "reconciling": {"description": "Output only. If `true`, the subscription is in the process of being updated.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The state of the subscription. Determines whether the subscription can receive events and deliver them to the notification endpoint.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "SUSPENDED", "DELETED"], "enumDescriptions": ["Default value. This value is unused.", "The subscription is active and can receive and deliver events to its notification endpoint.", "The subscription is unable to receive events due to an error. To identify the error, see the `suspension_reason` field.", "The subscription is deleted."], "readOnly": true, "type": "string"}, "suspensionReason": {"description": "Output only. The error that suspended the subscription. To reactivate the subscription, resolve the error and call the `ReactivateSubscription` method.", "enum": ["ERROR_TYPE_UNSPECIFIED", "USER_SCOPE_REVOKED", "RESOURCE_DELETED", "USER_AUTHORIZATION_FAILURE", "ENDPOINT_PERMISSION_DENIED", "ENDPOINT_NOT_FOUND", "ENDPOINT_RESOURCE_EXHAUSTED", "OTHER"], "enumDescriptions": ["Default value. This value is unused.", "The authorizing user has revoked the grant of one or more OAuth scopes. To learn more about authorization for Google Workspace, see [Configure the OAuth consent screen](https://developers.google.com/workspace/guides/configure-oauth-consent#choose-scopes).", "The target resource for the subscription no longer exists.", "The user that authorized the creation of the subscription no longer has access to the subscription's target resource.", "The Google Workspace application doesn't have access to deliver events to your subscription's notification endpoint.", "The subscription's notification endpoint doesn't exist, or the endpoint can't be found in the Google Cloud project where you created the subscription.", "The subscription's notification endpoint failed to receive events due to insufficient quota or reaching rate limiting.", "An unidentified error has occurred."], "readOnly": true, "type": "string"}, "targetResource": {"description": "Required. Immutable. The Google Workspace resource that's monitored for events, formatted as the [full resource name](https://google.aip.dev/122#full-resource-names). To learn about target resources and the events that they support, see [Supported Google Workspace events](https://developers.google.com/workspace/events#supported-events). A user can only authorize your app to create one subscription for a given target resource. If your app tries to create another subscription with the same user credentials, the request returns an `ALREADY_EXISTS` error.", "type": "string"}, "ttl": {"description": "Input only. The time-to-live (TTL) or duration for the subscription. If unspecified or set to `0`, uses the maximum possible duration.", "format": "google-duration", "type": "string"}, "uid": {"description": "Output only. System-assigned unique identifier for the subscription.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last time that the subscription is updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Google Workspace Events API", "version": "v1", "version_module": true}