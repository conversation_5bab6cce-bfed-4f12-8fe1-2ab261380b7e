<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Wrapper Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .demo-section h2 {
            color: #4a5568;
            margin-bottom: 15px;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        button:hover:not(:disabled) {
            background: #5a67d8;
        }
        
        button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
        }
        
        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            min-height: 100px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .loading {
            color: #718096;
            font-style: italic;
        }
        
        .error {
            color: #e53e3e;
            background: #fed7d7;
            border-color: #feb2b2;
        }
        
        .success {
            color: #2d3748;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #48bb78;
        }
        
        .status-offline {
            background: #e53e3e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 LLM Wrapper Demo</h1>
        
        <div class="demo-section">
            <h2><span id="status-indicator" class="status-indicator status-offline"></span>API Status</h2>
            <p id="status-text">Checking connection...</p>
            <button onclick="checkStatus()">Refresh Status</button>
        </div>
        
        <div class="demo-section">
            <h2>💬 Chat Interface</h2>
            <div class="input-group">
                <label for="message">Your Message:</label>
                <textarea id="message" placeholder="Ask me anything about spiritual guidance, biblical interpretation, or just have a conversation...">Hello, can you provide some guidance for finding peace in difficult times?</textarea>
            </div>
            
            <div class="input-group">
                <label for="interaction-type">Interaction Type:</label>
                <select id="interaction-type">
                    <option value="general">General</option>
                    <option value="daily_guidance">Daily Guidance</option>
                    <option value="interpretation">Interpretation</option>
                    <option value="conversational">Conversational</option>
                    <option value="therapeutic">Therapeutic</option>
                </select>
            </div>
            
            <button onclick="sendMessage()" id="send-btn">Send Message</button>
            
            <div id="chat-response" class="response-area">
                Click "Send Message" to test the chat functionality...
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📋 API Information</h2>
            <p><strong>Backend URL:</strong> http://localhost:8080</p>
            <p><strong>Working Endpoint:</strong> POST /api/chat/</p>
            <p><strong>LLM Provider:</strong> Google Gemini API</p>
            <p><strong>Content Domain:</strong> Biblical Texts</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        async function checkStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            statusText.textContent = 'Checking...';
            statusIndicator.className = 'status-indicator status-offline';
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'test',
                        interaction_type: 'general'
                    })
                });
                
                if (response.ok) {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = 'API is online and responding';
                } else {
                    statusIndicator.className = 'status-indicator status-offline';
                    statusText.textContent = `API error: ${response.status}`;
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Cannot connect to API. Make sure Django server is running on port 8080.';
            }
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('message');
            const interactionType = document.getElementById('interaction-type');
            const responseArea = document.getElementById('chat-response');
            const sendBtn = document.getElementById('send-btn');
            
            const message = messageInput.value.trim();
            if (!message) {
                alert('Please enter a message');
                return;
            }
            
            sendBtn.disabled = true;
            sendBtn.textContent = 'Sending...';
            responseArea.className = 'response-area loading';
            responseArea.textContent = 'Sending your message to the AI...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        interaction_type: interactionType.value,
                        session_id: 'demo-session-' + Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success !== false) {
                    responseArea.className = 'response-area success';
                    responseArea.textContent = data.response || data.guidance || 'No response received';
                } else {
                    responseArea.className = 'response-area error';
                    responseArea.textContent = `Error: ${data.error || data.response || 'Unknown error'}`;
                }
            } catch (error) {
                responseArea.className = 'response-area error';
                responseArea.textContent = `Network error: ${error.message}`;
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send Message';
            }
        }
        
        // Check status on page load
        window.addEventListener('load', checkStatus);
        
        // Allow Enter key to send message
        document.getElementById('message').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                sendMessage();
            }
        });
    </script>
</body>
</html>
