"""
Chat App URL Configuration
"""

from django.urls import path
from . import views

app_name = 'chat'

urlpatterns = [
    path('', views.ChatView.as_view(), name='chat'),
    path('daily-guidance/', views.DailyGuidanceView.as_view(), name='daily_guidance'),
    path('domains/', views.ContentDomainView.as_view(), name='content_domains'),
    path('domains/switch/', views.ContentDomainView.as_view(), name='domain_switch'),
    path('history/', views.ChatHistoryView.as_view(), name='chat_history'),
    path('health/', views.health_check, name='health_check'),
    path('load-content/', views.load_content, name='load_content'),
]
