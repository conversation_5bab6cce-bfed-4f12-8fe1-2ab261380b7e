# AI-Powered Wisdom - Modular Multi-Domain Guidance Platform

A sophisticated, highly modular AI application that provides personalized guidance through multiple wisdom domains including Biblical Texts, Buddhist Teachings, and Self-Help Philosophy. Features both chat and speech interfaces with seamless domain switching and context-aware responses.

## 🏗️ Architecture Overview

### Core Design Principles
- **Content as a Black Box**: All content is stored in swappable modules
- **LLM as a Black Box**: LLM integration is abstracted for easy provider switching
- **Plugin Architecture**: New content domains can be added without code changes
- **Future-Ready**: Designed for RAG, document retrieval, and external context injection

### Technology Stack
- **Backend**: Django + Django REST Framework
- **Frontend**: React + Vite
- **LLM Integration**: Google Gemini API (easily swappable)
- **Speech Processing**: AssemblyAI (Speech-to-Text) + Web Speech API (Text-to-Speech)
- **Database**: SQLite (development) / PostgreSQL (production ready)

## 🚀 Features

### Chat Interface
- Multiple interaction types (Daily Guidance, Interpretation, Conversational, Therapeutic)
- Context-aware responses using domain-specific content
- Session management and chat history
- Real-time response streaming

### Speech Interface
- Speech-to-text conversion using AssemblyAI
- Text-to-speech using Web Speech API
- Complete speech-based conversations
- Voice interaction controls

### Content Management
- **Three Active Domains**: Biblical Texts, Buddhist Teachings, Self-Help Philosophy
- **Real-time Domain Switching**: Seamless switching between wisdom traditions
- **Domain-Specific Instructions**: Each domain has tailored AI behavior and responses
- **Rich Content Database**: Categorized content with search and retrieval capabilities
- **Bulk Content Import**: JSON-based content loading system

### LLM Integration
- Abstracted LLM service layer
- Currently supports Google Gemini API
- Easy to add new providers (OpenAI, Anthropic, etc.)
- Configurable interaction types and contexts

## 📁 Project Structure

```
llm_wrapper/
├── llm_wrapper_backend/          # Django project settings
├── chat/                         # Chat interface app
├── speech/                       # Speech processing app
├── content_management/           # Content domain management
├── llm_integration/             # LLM provider abstractions
├── frontend/                    # React frontend
├── content/                     # Content domains
│   └── domains/
│       ├── biblical_texts/      # Biblical wisdom and teachings
│       ├── buddhist_teachings/  # Buddhist philosophy and meditation
│       └── self_help/          # Personal development and motivation
└── static/                      # Static files
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- Git

### Backend Setup

1. **Clone and navigate to the project**
```bash
cd llm_wrapper
```

2. **Create virtual environment**
```bash
python -m venv venv
# Windows
.\venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **Install Python dependencies**
```bash
pip install django djangorestframework django-cors-headers python-dotenv google-generativeai assemblyai requests
```

4. **Configure environment variables**
Create a `.env` file in the root directory:
```env
# LLM API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Speech API Keys
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# Django Settings
SECRET_KEY=your_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173

# Content Domain Configuration
ACTIVE_CONTENT_DOMAIN=biblical_texts
CONTENT_BASE_PATH=content/domains/
```

5. **Run database migrations**
```bash
python manage.py makemigrations
python manage.py migrate
```

6. **Load sample content for all domains**
```bash
# Load Biblical content
python manage.py load_sample_content --domain biblical_texts --file content/domains/biblical_texts/sample_content.json

# Load Buddhist content
python manage.py load_sample_content --domain buddhist_teachings --file content/domains/buddhist_teachings/sample_content.json

# Load Self-Help content
python manage.py load_sample_content --domain self_help --file content/domains/self_help/sample_content.json
```

7. **Start the CORS proxy (recommended)**
```bash
python cors_proxy.py
```

8. **Start the Django server**
```bash
python manage.py runserver 8080
```

### Frontend Setup

1. **Navigate to frontend directory**
```bash
cd frontend
```

2. **Install Node.js dependencies**
```bash
npm install
```

3. **Start the development server**
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## 🌟 Available Wisdom Domains

### Biblical Texts
- **Focus**: Biblical wisdom, spiritual guidance, and Christian teachings
- **Content**: Verses, parables, and spiritual insights from the Bible
- **Interaction Style**: Compassionate spiritual guidance with biblical references
- **Use Cases**: Daily devotions, spiritual counseling, biblical interpretation

### Buddhist Teachings
- **Focus**: Mindfulness, meditation, and Buddhist philosophy
- **Content**: Four Noble Truths, Eightfold Path, meditation practices, compassion teachings
- **Interaction Style**: Wise Buddhist teacher emphasizing mindfulness and enlightenment
- **Use Cases**: Meditation guidance, mindfulness practice, philosophical discussions

### Self-Help Philosophy
- **Focus**: Personal development, motivation, and practical life strategies
- **Content**: Goal-setting techniques, productivity methods, positive psychology
- **Interaction Style**: Motivational life coach with actionable advice
- **Use Cases**: Goal achievement, productivity improvement, personal growth

## 🔧 API Documentation

### Chat Endpoints

#### POST /api/chat/
Send a chat message and receive AI response.

**Request:**
```json
{
  "message": "How can I find peace in difficult times?",
  "interaction_type": "therapeutic",
  "session_id": "optional-session-id",
  "domain_slug": "buddhist_teachings"
}
```

**Response:**
```json
{
  "response": "AI generated response...",
  "interaction_type": "therapeutic",
  "session_id": "session-uuid",
  "success": true,
  "response_time": 1.23,
  "model_info": {
    "provider": "gemini",
    "model": "gemini-1.5-flash"
  }
}
```

#### POST /api/chat/daily-guidance/
Get daily spiritual guidance.

#### GET /api/chat/domains/
List available content domains.

#### POST /api/chat/domains/
Switch active content domain.

### Speech Endpoints

#### POST /api/speech/stt/
Convert speech to text.

#### POST /api/speech/tts/
Convert text to speech.

#### POST /api/speech/chat/
Complete speech-based conversation (STT → LLM → TTS).

## 🔌 Adding New Content Domains

### 1. Create Content Structure
```json
{
  "domain": {
    "name": "Your Domain Name",
    "slug": "your_domain_slug",
    "description": "Domain description",
    "config": {
      "instructions": {
        "daily_guidance": "Instructions for daily guidance...",
        "interpretation": "Instructions for interpretation...",
        "conversational": "Instructions for conversation...",
        "therapeutic": "Instructions for therapeutic responses...",
        "general": "General instructions..."
      }
    }
  },
  "categories": [
    {
      "name": "Category Name",
      "slug": "category_slug",
      "description": "Category description",
      "content": [
        {
          "title": "Content Title",
          "content": "Content text...",
          "reference": "Source reference",
          "type": "text",
          "tags": ["tag1", "tag2"],
          "metadata": {}
        }
      ]
    }
  ]
}
```

### 2. Load Content
```bash
python manage.py load_sample_content --domain your_domain_slug --file path/to/content.json
```

## 🔄 Adding New LLM Providers

### 1. Create Provider Class
```python
class NewLLMProvider(LLMProvider):
    def __init__(self):
        self.api_key = getattr(settings, 'NEW_LLM_API_KEY', None)
        # Initialize provider
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # Implement response generation
        pass
    
    def is_configured(self) -> bool:
        # Check configuration
        return self.api_key is not None
```

### 2. Register Provider
```python
# In llm_integration/services.py
self.providers = {
    'gemini': GeminiProvider(),
    'new_provider': NewLLMProvider(),
}
```

## 🧪 Testing

### Run Backend Tests
```bash
python manage.py test
```

### Test API Endpoints
```bash
# Test health endpoint
curl http://localhost:8080/api/chat/health/

# Test domains endpoint
curl http://localhost:8080/api/chat/domains/

# Test chat endpoint
curl -X POST http://localhost:8080/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "interaction_type": "general"}'
```

## 🚀 Deployment

### Environment Variables for Production
```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgresql://user:password@localhost/dbname
```

### Static Files
```bash
python manage.py collectstatic
```

### Frontend Build
```bash
cd frontend
npm run build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in runserver command
2. **API key errors**: Verify your API keys in the .env file
3. **CORS errors**: Check CORS_ALLOWED_ORIGINS in settings
4. **Database errors**: Run migrations with `python manage.py migrate`

### Getting Help

- Check the logs in the Django console
- Verify API endpoints with curl
- Ensure all dependencies are installed
- Check that the virtual environment is activated

## 🔮 Future Enhancements

- RAG integration for document retrieval
- Multi-language support
- Advanced analytics and usage tracking
- Mobile app development
- Voice cloning and custom TTS
- Integration with external knowledge bases
