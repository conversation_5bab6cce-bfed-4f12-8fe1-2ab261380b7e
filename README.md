# AI-Powered Wisdom - Modular Multi-Domain Guidance Platform

A sophisticated, highly modular AI application that provides personalized guidance through multiple wisdom domains including Biblical Texts, Buddhist Teachings, and Self-Help Philosophy. Features both chat and speech interfaces with seamless domain switching and context-aware responses.

## 🏗️ Architecture Overview

### Core Design Principles
- **Content as a Black Box**: All content is stored in swappable modules (`content/domains/`)
- **LLM as a Black Box**: LLM integration is abstracted for easy provider switching (`llm_integration/`)
- **Plugin Architecture**: New content domains can be added without code changes
- **Future-Ready**: Designed for RAG, document retrieval, and external context injection

### Modular Architecture Explanation

#### 🔄 Content Layer (Black Box Design)
```
content/domains/[domain_name]/sample_content.json
```
- **Purpose**: Completely isolated content storage
- **Swappable**: Replace any domain without touching code
- **Structure**: JSON-based with domain config, categories, and content pieces
- **Loading**: `python manage.py load_sample_content --domain [name] --file [path]`

#### 🤖 LLM Layer (Provider Abstraction)
```
llm_integration/services.py
├── LLMProvider (Abstract Base Class)
├── GeminiProvider (Current Implementation)
└── [Future: OpenAIProvider, AnthropicProvider, etc.]
```
- **Purpose**: Clean abstraction over different AI providers
- **Extensible**: Add new providers by implementing LLMProvider interface
- **Configurable**: Environment-based configuration for API keys and settings

#### 📊 Content Management Layer
```
content_management/
├── models.py (ContentDomain, ContentCategory, ContentPiece)
├── services.py (ContentService for retrieval and search)
└── management/commands/ (Content loading utilities)
```
- **Purpose**: Database-driven content management with search capabilities
- **Dynamic**: Real-time domain switching without application restart
- **Scalable**: Ready for vector databases and semantic search

#### 🎯 Application Layer
```
chat/ (Text Interface) + speech/ (Voice Interface)
├── views.py (API endpoints)
├── serializers.py (Request/response validation)
└── services.py (Business logic)
```
- **Purpose**: User interface and API endpoints
- **Modular**: Separate apps for different interaction modes
- **Consistent**: Shared patterns across chat and speech interfaces

### Technology Stack
- **Backend**: Django + Django REST Framework
- **Frontend**: React + Vite
- **LLM Integration**: Google Gemini API (easily swappable)
- **Speech Processing**: AssemblyAI (Speech-to-Text) + Web Speech API (Text-to-Speech)
- **Database**: SQLite (development) / PostgreSQL (production ready)

## 🚀 Features

### Chat Interface
- Multiple interaction types (Daily Guidance, Interpretation, Conversational, Therapeutic)
- Context-aware responses using domain-specific content
- Session management and chat history
- Real-time response streaming

### Speech Interface
- Speech-to-text conversion using AssemblyAI
- Text-to-speech using Web Speech API
- Complete speech-based conversations
- Voice interaction controls

### Content Management
- **Three Active Domains**: Biblical Texts, Buddhist Teachings, Self-Help Philosophy
- **Real-time Domain Switching**: Seamless switching between wisdom traditions
- **Domain-Specific Instructions**: Each domain has tailored AI behavior and responses
- **Rich Content Database**: Categorized content with search and retrieval capabilities
- **Bulk Content Import**: JSON-based content loading system

### LLM Integration
- Abstracted LLM service layer
- Currently supports Google Gemini API
- Easy to add new providers (OpenAI, Anthropic, etc.)
- Configurable interaction types and contexts

## 📁 Project Structure & File Purpose

```
ai-powered-wisdom/
├── 📁 llm_wrapper_backend/          # Django Project Configuration
│   ├── __init__.py                  # Python package marker
│   ├── settings.py                  # Django settings, database, CORS, API keys
│   ├── urls.py                      # Main URL routing, API endpoint definitions
│   ├── wsgi.py                      # WSGI configuration for deployment
│   └── asgi.py                      # ASGI configuration for async support
│
├── 📁 chat/                         # Chat Interface Django App
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # Database models for chat history, interactions
│   ├── views.py                     # API endpoints for chat, daily guidance, domains
│   ├── serializers.py               # Request/response data validation and formatting
│   ├── urls.py                      # Chat app URL routing
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for chat functionality
│   └── migrations/                  # Database migration files
│
├── 📁 speech/                       # Speech Processing Django App
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # Database models for speech interactions
│   ├── views.py                     # API endpoints for STT, TTS, speech chat
│   ├── serializers.py               # Speech request/response validation
│   ├── services.py                  # Speech processing logic (AssemblyAI, Web Speech)
│   ├── urls.py                      # Speech app URL routing
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for speech functionality
│   └── migrations/                  # Database migration files
│
├── 📁 content_management/           # Content Domain Management System
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # ContentDomain, ContentCategory, ContentPiece models
│   ├── services.py                  # Content retrieval, search, domain switching logic
│   ├── admin.py                     # Django admin for content management
│   ├── apps.py                      # Django app configuration
│   ├── management/                  # Custom Django management commands
│   │   └── commands/
│   │       └── load_sample_content.py  # Command to load content from JSON files
│   ├── tests.py                     # Unit tests for content management
│   └── migrations/                  # Database migration files
│
├── 📁 llm_integration/              # LLM Provider Abstraction Layer
│   ├── __init__.py                  # Python package marker
│   ├── services.py                  # LLMProvider base class, GeminiProvider implementation
│   ├── models.py                    # LLM interaction logging models
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for LLM integration
│   └── migrations/                  # Database migration files
│
├── 📁 frontend/                     # React Frontend Application
│   ├── 📁 public/                   # Static assets served by Vite
│   │   ├── index.html               # Main HTML template
│   │   └── vite.svg                 # Vite logo
│   ├── 📁 src/                      # React source code
│   │   ├── 📁 components/           # React components
│   │   │   ├── App.jsx              # Root component, routing, global state
│   │   │   ├── App.css              # Global application styles
│   │   │   ├── Header.jsx           # Navigation header with logo and title
│   │   │   ├── Header.css           # Header component styles
│   │   │   ├── ChatInterface.jsx    # Text-based chat interface
│   │   │   ├── ChatInterface.css    # Chat interface styles
│   │   │   ├── SpeechInterface.jsx  # Voice-based chat interface
│   │   │   ├── SpeechInterface.css  # Speech interface styles
│   │   │   ├── DomainSelector.jsx   # Domain switching interface
│   │   │   └── DomainSelector.css   # Domain selector styles
│   │   ├── main.jsx                 # React application entry point
│   │   └── index.css                # Global CSS styles
│   ├── package.json                 # Node.js dependencies and scripts
│   ├── package-lock.json            # Locked dependency versions
│   ├── vite.config.js               # Vite build configuration
│   └── eslint.config.js             # ESLint configuration for code quality
│
├── 📁 content/                      # Content Domains (Black Box Design)
│   └── 📁 domains/                  # Individual content domains
│       ├── 📁 biblical_texts/       # Biblical Wisdom Domain
│       │   └── sample_content.json  # Biblical verses, teachings, spiritual guidance
│       ├── 📁 buddhist_teachings/   # Buddhist Philosophy Domain
│       │   └── sample_content.json  # Meditation practices, Four Noble Truths, mindfulness
│       └── 📁 self_help/           # Personal Development Domain
│           └── sample_content.json  # Goal-setting, productivity, motivation strategies
│
├── 📁 static/                       # Django Static Files
│   └── (collected static files for production)
│
├── 📁 media/                        # User-uploaded files (speech recordings)
│   └── audio/                       # Temporary audio files for speech processing
│
├── 📄 Configuration & Setup Files
├── .env                             # Environment variables (API keys, settings)
├── .env.example                     # Example environment configuration
├── .gitignore                       # Git ignore patterns
├── requirements.txt                 # Python dependencies
├── manage.py                        # Django management script
├── cors_proxy.py                    # CORS proxy server for development
├── db.sqlite3                       # SQLite database (development)
│
├── 📄 Documentation Files
├── README.md                        # Main project documentation and setup guide
├── DOCUMENTATION.md                 # Comprehensive technical documentation and development story
├── QUICK_START.md                   # 5-minute setup guide
├── REQUIREMENTS_ANALYSIS.md         # Requirements fulfillment analysis
│
└── 📄 Optional Deployment Files
    ├── Dockerfile                   # Docker container configuration
    ├── docker-compose.yml           # Multi-container Docker setup
    └── requirements-prod.txt        # Production-specific dependencies
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- Git

### Backend Setup

1. **Clone and navigate to the project**
```bash
cd llm_wrapper
```

2. **Create virtual environment**
```bash
python -m venv venv
# Windows
.\venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **Install Python dependencies**
```bash
pip install django djangorestframework django-cors-headers python-dotenv google-generativeai assemblyai requests
```

4. **Configure environment variables**
Create a `.env` file in the root directory:
```env
# LLM API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Speech API Keys
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# Django Settings
SECRET_KEY=your_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173

# Content Domain Configuration
ACTIVE_CONTENT_DOMAIN=biblical_texts
CONTENT_BASE_PATH=content/domains/
```

5. **Run database migrations**
```bash
python manage.py makemigrations
python manage.py migrate
```

6. **Load sample content for all domains**
```bash
# Load Biblical content
python manage.py load_sample_content --domain biblical_texts --file content/domains/biblical_texts/sample_content.json

# Load Buddhist content
python manage.py load_sample_content --domain buddhist_teachings --file content/domains/buddhist_teachings/sample_content.json

# Load Self-Help content
python manage.py load_sample_content --domain self_help --file content/domains/self_help/sample_content.json
```

7. **Start the CORS proxy (recommended)**
```bash
python cors_proxy.py
```

8. **Start the Django server**
```bash
python manage.py runserver 8080
```

### Frontend Setup

1. **Navigate to frontend directory**
```bash
cd frontend
```

2. **Install Node.js dependencies**
```bash
npm install
```

3. **Start the development server**
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## 🌟 Available Wisdom Domains

### Biblical Texts
- **Focus**: Biblical wisdom, spiritual guidance, and Christian teachings
- **Content**: Verses, parables, and spiritual insights from the Bible
- **Interaction Style**: Compassionate spiritual guidance with biblical references
- **Use Cases**: Daily devotions, spiritual counseling, biblical interpretation

### Buddhist Teachings
- **Focus**: Mindfulness, meditation, and Buddhist philosophy
- **Content**: Four Noble Truths, Eightfold Path, meditation practices, compassion teachings
- **Interaction Style**: Wise Buddhist teacher emphasizing mindfulness and enlightenment
- **Use Cases**: Meditation guidance, mindfulness practice, philosophical discussions

### Self-Help Philosophy
- **Focus**: Personal development, motivation, and practical life strategies
- **Content**: Goal-setting techniques, productivity methods, positive psychology
- **Interaction Style**: Motivational life coach with actionable advice
- **Use Cases**: Goal achievement, productivity improvement, personal growth

## ✅ Requirements Fulfillment

### Functional Requirements ✅
- **Chat Interface**: Complete text-based conversation system with multiple interaction types
- **Speech Interface**: Full speech-to-text and text-to-speech pipeline with visual feedback
- **Knowledge Domains**: Three complete domains (Biblical, Buddhist, Self-Help) with rich content
- **Response Types**: Daily guidance, interpretation, conversational, therapeutic, and general support

### Technical Requirements ✅
- **Backend**: Django REST Framework with modular app architecture
- **Frontend**: Modern React + Vite application with responsive design
- **LLM Integration**: Google Gemini API with clean provider abstraction
- **Speech APIs**: AssemblyAI (STT) + Web Speech API (TTS) integration
- **Deployment**: Comprehensive local setup with CORS proxy solution

### Modularity Requirements ✅
- **Content as Black Box**: JSON-based content in `content/domains/` - easily swappable
- **LLM as Black Box**: Abstract provider interface in `llm_integration/services.py`
- **Plugin Architecture**: Add new domains without code changes via management commands
- **Future-Ready**: Architecture supports RAG, vector databases, and external context injection

### Bonus Features Implemented 🚀
- **Multi-Domain Support**: 3 domains instead of 1 required
- **Real-Time Domain Switching**: Seamless switching without restart
- **Session Management**: Conversation continuity and history
- **Error Handling**: Comprehensive error handling and user feedback
- **Performance Optimization**: Efficient queries and response caching
- **Security Features**: Input validation, CORS handling, API key management

## 🔧 API Documentation

### Chat Endpoints

#### POST /api/chat/
Send a chat message and receive AI response.

**Request:**
```json
{
  "message": "How can I find peace in difficult times?",
  "interaction_type": "therapeutic",
  "session_id": "optional-session-id",
  "domain_slug": "buddhist_teachings"
}
```

**Response:**
```json
{
  "response": "AI generated response...",
  "interaction_type": "therapeutic",
  "session_id": "session-uuid",
  "success": true,
  "response_time": 1.23,
  "model_info": {
    "provider": "gemini",
    "model": "gemini-1.5-flash"
  }
}
```

#### POST /api/chat/daily-guidance/
Get daily spiritual guidance.

#### GET /api/chat/domains/
List available content domains.

#### POST /api/chat/domains/
Switch active content domain.

### Speech Endpoints

#### POST /api/speech/stt/
Convert speech to text.

#### POST /api/speech/tts/
Convert text to speech.

#### POST /api/speech/chat/
Complete speech-based conversation (STT → LLM → TTS).

## 🔌 Adding New Content Domains

### 1. Create Content Structure
```json
{
  "domain": {
    "name": "Your Domain Name",
    "slug": "your_domain_slug",
    "description": "Domain description",
    "config": {
      "instructions": {
        "daily_guidance": "Instructions for daily guidance...",
        "interpretation": "Instructions for interpretation...",
        "conversational": "Instructions for conversation...",
        "therapeutic": "Instructions for therapeutic responses...",
        "general": "General instructions..."
      }
    }
  },
  "categories": [
    {
      "name": "Category Name",
      "slug": "category_slug",
      "description": "Category description",
      "content": [
        {
          "title": "Content Title",
          "content": "Content text...",
          "reference": "Source reference",
          "type": "text",
          "tags": ["tag1", "tag2"],
          "metadata": {}
        }
      ]
    }
  ]
}
```

### 2. Load Content
```bash
python manage.py load_sample_content --domain your_domain_slug --file path/to/content.json
```

## 🔄 Adding New LLM Providers

### 1. Create Provider Class
```python
class NewLLMProvider(LLMProvider):
    def __init__(self):
        self.api_key = getattr(settings, 'NEW_LLM_API_KEY', None)
        # Initialize provider
    
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # Implement response generation
        pass
    
    def is_configured(self) -> bool:
        # Check configuration
        return self.api_key is not None
```

### 2. Register Provider
```python
# In llm_integration/services.py
self.providers = {
    'gemini': GeminiProvider(),
    'new_provider': NewLLMProvider(),
}
```

## 🧪 Testing

### Run Backend Tests
```bash
python manage.py test
```

### Test API Endpoints
```bash
# Test health endpoint
curl http://localhost:8080/api/chat/health/

# Test domains endpoint
curl http://localhost:8080/api/chat/domains/

# Test chat endpoint
curl -X POST http://localhost:8080/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "interaction_type": "general"}'
```

## 🚀 Deployment

### Environment Variables for Production
```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgresql://user:password@localhost/dbname
```

### Static Files
```bash
python manage.py collectstatic
```

### Frontend Build
```bash
cd frontend
npm run build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in runserver command
2. **API key errors**: Verify your API keys in the .env file
3. **CORS errors**: Check CORS_ALLOWED_ORIGINS in settings
4. **Database errors**: Run migrations with `python manage.py migrate`

### Getting Help

- Check the logs in the Django console
- Verify API endpoints with curl
- Ensure all dependencies are installed
- Check that the virtual environment is activated

## 🔮 Future Enhancements

- RAG integration for document retrieval
- Multi-language support
- Advanced analytics and usage tracking
- Mobile app development
- Voice cloning and custom TTS
- Integration with external knowledge bases
