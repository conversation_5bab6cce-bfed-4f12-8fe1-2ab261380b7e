# 🚀 Quick Start Guide - AI-Powered Wisdom

## ⚡ 5-Minute Setup

### Prerequisites
- Python 3.8+ installed
- Node.js 16+ installed
- Git installed

### 1. <PERSON><PERSON> and Setup Backend
```bash
# Clone the repository
git clone <repository-url>
cd ai-powered-wisdom

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
.\venv\Scripts\activate
# Mac/Linux:
source venv/bin/activate

# Install dependencies
pip install django djangorestframework django-cors-headers python-dotenv google-generativeai assemblyai requests
```

### 2. Configure Environment
Create `.env` file in root directory:
```env
GEMINI_API_KEY=your_gemini_api_key_here
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here
SECRET_KEY=your_secret_key_here
DEBUG=True
```

### 3. Setup Database and Content
```bash
# Run migrations
python manage.py migrate

# Load all content domains
python manage.py load_sample_content --domain biblical_texts --file content/domains/biblical_texts/sample_content.json
python manage.py load_sample_content --domain buddhist_teachings --file content/domains/buddhist_teachings/sample_content.json
python manage.py load_sample_content --domain self_help --file content/domains/self_help/sample_content.json
```

### 4. Start Backend Services
```bash
# Terminal 1: Start CORS proxy
python cors_proxy.py

# Terminal 2: Start Django server
python manage.py runserver 8080
```

### 5. Setup and Start Frontend
```bash
# New terminal
cd frontend
npm install
npm run dev
```

### 6. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8081 (via CORS proxy)
- **Direct Backend**: http://localhost:8080

## 🎯 Quick Test

1. Open http://localhost:5173
2. Go to Settings → Switch to "Buddhist Teachings"
3. Ask: "What is non-violence?"
4. Expected: Response about ahimsa and Buddhist compassion
5. Switch to "Self-Help Philosophy"
6. Ask the same question
7. Expected: Response about conflict resolution and assertiveness

## 🔧 Common Issues

### Issue: CORS Errors
**Solution**: Make sure CORS proxy is running on port 8081

### Issue: API Key Errors
**Solution**: Check your `.env` file has valid API keys

### Issue: Content Not Loading
**Solution**: Re-run the content loading commands

### Issue: Domain Switching Not Working
**Solution**: Check browser network tab for successful API calls

## 📚 Key Features to Try

### Chat Interface
- Try different interaction types (Daily Guidance, Therapeutic, etc.)
- Switch between domains and notice response style changes
- Use session management for conversation continuity

### Speech Interface
- Click microphone to record voice questions
- Enable text-to-speech for audio responses
- Test with different domains for varied response styles

### Domain Switching
- Biblical Texts: Ask about forgiveness, love, spiritual guidance
- Buddhist Teachings: Ask about meditation, mindfulness, suffering
- Self-Help: Ask about goals, productivity, motivation

## 🛠️ Development Tips

### Adding New Content
1. Create JSON file in `content/domains/new_domain/`
2. Follow the standard content structure
3. Load with: `python manage.py load_sample_content --domain new_domain --file path/to/content.json`

### Debugging
- Check Django logs in terminal
- Use browser developer tools for frontend issues
- Monitor network requests for API call status

### Testing API Directly
```bash
# Test domain switching
curl -X POST http://localhost:8081/api/domains/switch/ \
  -H "Content-Type: application/json" \
  -d '{"domain_slug": "buddhist_teachings"}'

# Test chat
curl -X POST http://localhost:8081/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "domain_slug": "buddhist_teachings"}'
```

## 📖 Next Steps

1. **Read Full Documentation**: Check `DOCUMENTATION.md` for complete technical details
2. **Explore API**: Review `README.md` for comprehensive API documentation
3. **Customize Content**: Add your own content domains and teachings
4. **Extend Features**: Add new LLM providers or interaction types

## 🆘 Getting Help

1. Check the troubleshooting section in `DOCUMENTATION.md`
2. Review error logs in Django terminal
3. Verify all services are running on correct ports
4. Ensure API keys are properly configured

## 🎉 Success Indicators

✅ Frontend loads without errors
✅ Domain switching works (check Settings page)
✅ Chat responses vary by domain
✅ Speech interface records and responds
✅ No CORS errors in browser console
✅ Backend logs show successful API calls

You're ready to explore the AI-Powered Wisdom platform!
