#!/usr/bin/env python3
"""
Test CORS functionality between <PERSON><PERSON> frontend and Django backend
"""

import requests
import json

def test_cors():
    print("🔧 Testing CORS Configuration")
    print("=" * 40)
    
    # Test 1: OPTIONS request (preflight)
    print("\n1. Testing OPTIONS (preflight) request...")
    try:
        response = requests.options(
            'http://localhost:8080/api/chat/',
            headers={
                'Origin': 'http://localhost:5173',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        )
        print(f"   Status: {response.status_code}")
        print(f"   CORS Headers: {dict(response.headers)}")
        
        if 'Access-Control-Allow-Origin' in response.headers:
            print("   ✅ CORS preflight: WORKING")
        else:
            print("   ⚠️ CORS preflight: Missing headers")
            
    except Exception as e:
        print(f"   ❌ OPTIONS request failed: {e}")
    
    # Test 2: POST request with Origin header
    print("\n2. Testing POST request with CORS...")
    try:
        response = requests.post(
            'http://localhost:8080/api/chat/',
            headers={
                'Origin': 'http://localhost:5173',
                'Content-Type': 'application/json'
            },
            json={
                'message': 'Test CORS functionality',
                'interaction_type': 'general'
            }
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ POST request: SUCCESS")
            print(f"   Response: {data.get('response', '')[:100]}...")
        else:
            print(f"   ❌ POST request failed: {response.text}")
            
    except Exception as e:
        print(f"   ❌ POST request failed: {e}")
    
    # Test 3: Frontend accessibility
    print("\n3. Testing Frontend accessibility...")
    try:
        response = requests.get('http://localhost:5173/')
        if response.status_code == 200:
            print("   ✅ React Frontend: ACCESSIBLE")
        else:
            print(f"   ❌ React Frontend: Status {response.status_code}")
    except Exception as e:
        print(f"   ❌ React Frontend: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 CORS Test Complete!")
    print("\nIf all tests pass, the CORS issue should be resolved.")
    print("Try using the React frontend at: http://localhost:5173/")

if __name__ == "__main__":
    test_cors()
