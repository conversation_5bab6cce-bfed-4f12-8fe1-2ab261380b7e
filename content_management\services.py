"""
Content Management Services

Provides services for managing content domains and retrieving context
for LLM interactions. Designed with plugin architecture for easy swapping.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from django.conf import settings
from django.db.models import Q
from .models import ContentDomain, ContentCategory, ContentPiece, ContentFile

logger = logging.getLogger(__name__)


class ContentProvider:
    """Base class for content providers"""
    
    def __init__(self, domain_slug: str):
        self.domain_slug = domain_slug
        self.domain = self._get_domain()
    
    def _get_domain(self) -> Optional[ContentDomain]:
        """Get the content domain"""
        try:
            return ContentDomain.objects.get(slug=self.domain_slug)
        except ContentDomain.DoesNotExist:
            logger.warning(f"Content domain '{self.domain_slug}' not found")
            return None
    
    def get_context_for_query(self, query: str, interaction_type: str = 'general') -> str:
        """Get relevant context for a user query"""
        if not self.domain:
            logger.warning("No domain available for context retrieval")
            return ""

        logger.info(f"Getting context for domain: {self.domain.slug}, query: {query[:50]}...")

        # Search for relevant content pieces
        relevant_content = self._search_content(query, interaction_type)

        logger.info(f"Found {len(relevant_content)} relevant content pieces")

        # Format context
        context = self._format_context(relevant_content, interaction_type)

        logger.info(f"Generated context length: {len(context)} characters")

        return context
    
    def _search_content(self, query: str, interaction_type: str) -> List[ContentPiece]:
        """Search for relevant content pieces"""
        if not self.domain:
            logger.warning("No domain available for content search")
            return []

        logger.info(f"Searching content in domain: {self.domain.slug}")

        # Basic text search across content pieces
        search_terms = query.lower().split()

        # Get total content count for this domain
        total_content = ContentPiece.objects.filter(category__domain=self.domain).count()
        logger.info(f"Total content pieces in {self.domain.slug}: {total_content}")

        # Build search query
        q_objects = Q()
        for term in search_terms:
            q_objects |= (
                Q(title__icontains=term) |
                Q(content__icontains=term) |
                Q(reference__icontains=term) |
                Q(tags__icontains=term)
            )

        # Filter by domain and search terms
        content_pieces = ContentPiece.objects.filter(
            category__domain=self.domain
        ).filter(q_objects)

        logger.info(f"Found {content_pieces.count()} matching pieces for query: {query}")

        # Prioritize featured content and limit results
        content_pieces = content_pieces.order_by('-is_featured', 'order')[:5]

        result = list(content_pieces)
        logger.info(f"Returning {len(result)} content pieces")

        # Log the titles of returned pieces for debugging
        for piece in result:
            logger.info(f"  - {piece.title} (from {piece.category.name})")

        return result
    
    def _format_context(self, content_pieces: List[ContentPiece], interaction_type: str) -> str:
        """Format content pieces into context string"""
        if not content_pieces:
            return ""
        
        context_parts = []
        
        for piece in content_pieces:
            formatted_piece = f"**{piece.title}**"
            if piece.reference:
                formatted_piece += f" ({piece.reference})"
            formatted_piece += f"\n{piece.content}\n"
            context_parts.append(formatted_piece)
        
        context = "\n".join(context_parts)
        
        # Add domain-specific instructions
        domain_context = self._get_domain_context(interaction_type)
        if domain_context:
            context = f"{domain_context}\n\n{context}"
        
        return context
    
    def _get_domain_context(self, interaction_type: str) -> str:
        """Get domain-specific context instructions"""
        if not self.domain or not self.domain.config:
            return ""
        
        domain_instructions = self.domain.config.get('instructions', {})
        return domain_instructions.get(interaction_type, "")
    
    def get_daily_content(self) -> Optional[ContentPiece]:
        """Get content for daily guidance"""
        if not self.domain:
            return None
        
        # Get featured content or random content
        featured_content = ContentPiece.objects.filter(
            category__domain=self.domain,
            is_featured=True
        ).first()
        
        if featured_content:
            return featured_content
        
        # Fallback to any content
        return ContentPiece.objects.filter(
            category__domain=self.domain
        ).order_by('?').first()
    
    def get_categories(self) -> List[ContentCategory]:
        """Get all categories for this domain"""
        if not self.domain:
            return []
        
        return list(self.domain.categories.all())
    
    def get_content_by_category(self, category_slug: str) -> List[ContentPiece]:
        """Get content pieces by category"""
        if not self.domain:
            return []
        
        try:
            category = self.domain.categories.get(slug=category_slug)
            return list(category.content_pieces.all())
        except ContentCategory.DoesNotExist:
            return []


class FileContentProcessor:
    """Processes content files for bulk import"""
    
    def __init__(self, content_file: ContentFile):
        self.content_file = content_file
        self.domain = content_file.domain
    
    def process(self) -> Dict[str, Any]:
        """Process the content file"""
        try:
            if self.content_file.file_type == 'txt':
                return self._process_text_file()
            elif self.content_file.file_type == 'json':
                return self._process_json_file()
            elif self.content_file.file_type == 'csv':
                return self._process_csv_file()
            else:
                return {'success': False, 'error': 'Unsupported file type'}
        
        except Exception as e:
            logger.error(f"Error processing content file {self.content_file.id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_text_file(self) -> Dict[str, Any]:
        """Process a plain text file"""
        try:
            with open(self.content_file.file.path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create a single content piece from the entire file
            category, created = ContentCategory.objects.get_or_create(
                domain=self.domain,
                slug='imported_content',
                defaults={'name': 'Imported Content', 'description': 'Content imported from files'}
            )
            
            ContentPiece.objects.create(
                category=category,
                title=self.content_file.name,
                content=content,
                content_type='text'
            )
            
            self.content_file.is_processed = True
            self.content_file.save()
            
            return {'success': True, 'message': 'Text file processed successfully'}
        
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _process_json_file(self) -> Dict[str, Any]:
        """Process a JSON file with structured content"""
        try:
            with open(self.content_file.file.path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_count = 0
            
            # Expect JSON structure: {"categories": [{"name": "", "content": []}]}
            for category_data in data.get('categories', []):
                category, created = ContentCategory.objects.get_or_create(
                    domain=self.domain,
                    slug=category_data.get('slug', category_data['name'].lower().replace(' ', '_')),
                    defaults={
                        'name': category_data['name'],
                        'description': category_data.get('description', '')
                    }
                )
                
                for content_data in category_data.get('content', []):
                    ContentPiece.objects.create(
                        category=category,
                        title=content_data.get('title', 'Untitled'),
                        content=content_data.get('content', ''),
                        content_type=content_data.get('type', 'text'),
                        reference=content_data.get('reference', ''),
                        tags=content_data.get('tags', []),
                        metadata=content_data.get('metadata', {})
                    )
                    processed_count += 1
            
            self.content_file.is_processed = True
            self.content_file.save()
            
            return {
                'success': True, 
                'message': f'JSON file processed successfully. {processed_count} content pieces created.'
            }
        
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _process_csv_file(self) -> Dict[str, Any]:
        """Process a CSV file"""
        # Implementation for CSV processing would go here
        return {'success': False, 'error': 'CSV processing not yet implemented'}


class ContentService:
    """Main service for content management"""

    def __init__(self):
        self.active_domain = None
        self.provider = None
        self._initialize()

    def _initialize(self):
        """Initialize the service, handling database not ready scenarios"""
        try:
            self.active_domain = self._get_active_domain()
            if self.active_domain:
                self.provider = ContentProvider(self.active_domain.slug)
        except Exception:
            # Database might not be ready (migrations not run yet)
            pass

    def _get_active_domain(self) -> Optional[ContentDomain]:
        """Get the currently active content domain"""
        try:
            return ContentDomain.objects.filter(is_active=True).first()
        except Exception:
            return None
    
    def get_context_for_interaction(self, query: str, interaction_type: str = 'general') -> str:
        """Get context for an LLM interaction"""
        if not self.provider:
            self._initialize()  # Try to initialize if not already done
            if not self.provider:
                return ""

        return self.provider.get_context_for_query(query, interaction_type)
    
    def get_daily_guidance_content(self) -> Optional[ContentPiece]:
        """Get content for daily guidance"""
        if not self.provider:
            return None
        
        return self.provider.get_daily_content()
    
    def switch_domain(self, domain_slug: str) -> bool:
        """Switch to a different content domain"""
        try:
            logger.info(f"Attempting to switch to domain: {domain_slug}")
            domain = ContentDomain.objects.get(slug=domain_slug)
            domain.activate()
            self.active_domain = domain
            self.provider = ContentProvider(domain_slug)
            logger.info(f"Successfully switched to domain: {domain_slug}")
            return True
        except ContentDomain.DoesNotExist:
            logger.error(f"Domain not found: {domain_slug}")
            return False
    
    def get_available_domains(self) -> List[ContentDomain]:
        """Get all available content domains"""
        return list(ContentDomain.objects.all())
    
    def process_content_file(self, content_file_id: int) -> Dict[str, Any]:
        """Process a content file for import"""
        try:
            content_file = ContentFile.objects.get(id=content_file_id)
            processor = FileContentProcessor(content_file)
            return processor.process()
        except ContentFile.DoesNotExist:
            return {'success': False, 'error': 'Content file not found'}


# Global service instance
content_service = ContentService()
