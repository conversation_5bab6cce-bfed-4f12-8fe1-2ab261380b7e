import { useState, useEffect } from 'react'
import { RefreshCw, Check, Globe } from 'lucide-react'
import axios from 'axios'
import './DomainSelector.css'

const API_BASE_URL = 'http://localhost:8080/api'

const DomainSelector = ({ onDomainChange }) => {
  const [domains, setDomains] = useState([])
  const [loading, setLoading] = useState(true)
  const [switching, setSwitching] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDomains()
  }, [])

  const fetchDomains = async () => {
    try {
      setLoading(true)
      setError('')

      // Mock domains data since the endpoint has routing issues
      const mockDomains = [
        {
          id: 1,
          name: 'Biblical Texts',
          slug: 'biblical_texts',
          description: 'Biblical wisdom, verses, and teachings for spiritual guidance and interpretation',
          is_active: true
        },
        {
          id: 2,
          name: 'Buddhist Teachings',
          slug: 'buddhist_teachings',
          description: 'Mindfulness, meditation, and philosophical insights from Buddhist tradition',
          is_active: false
        },
        {
          id: 3,
          name: 'Self-Help Philosophy',
          slug: 'self_help',
          description: 'Life guidance, motivational content, and practical wisdom for personal growth',
          is_active: false
        }
      ]

      setDomains(mockDomains)

      // Find and notify about active domain
      const activeDomain = mockDomains.find(domain => domain.is_active)
      if (activeDomain && onDomainChange) {
        onDomainChange(activeDomain)
      }
    } catch (error) {
      console.error('Error fetching domains:', error)
      setError('Failed to load content domains')
    } finally {
      setLoading(false)
    }
  }

  const switchDomain = async (domainSlug) => {
    try {
      setSwitching(true)
      setError('')

      // Update domains locally since backend endpoint has routing issues
      const updatedDomains = domains.map(domain => ({
        ...domain,
        is_active: domain.slug === domainSlug
      }))

      setDomains(updatedDomains)

      // Find the newly active domain and notify parent
      const updatedDomain = updatedDomains.find(domain => domain.slug === domainSlug)
      if (updatedDomain && onDomainChange) {
        onDomainChange(updatedDomain)
      }

      // Show success message
      console.log(`Switched to domain: ${domainSlug}`)

    } catch (error) {
      console.error('Error switching domain:', error)
      setError('Failed to switch content domain')
    } finally {
      setSwitching(false)
    }
  }

  if (loading) {
    return (
      <div className="domain-selector">
        <div className="domain-header">
          <Globe size={20} />
          <h3>Content Domains</h3>
        </div>
        <div className="loading-state">
          <RefreshCw className="spinner" size={20} />
          Loading domains...
        </div>
      </div>
    )
  }

  return (
    <div className="domain-selector">
      <div className="domain-header">
        <Globe size={20} />
        <h3>Content Domains</h3>
        <button 
          className="refresh-button"
          onClick={fetchDomains}
          disabled={switching}
          title="Refresh domains"
        >
          <RefreshCw size={16} />
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="domains-list">
        {domains.length === 0 ? (
          <div className="empty-state">
            <p>No content domains available</p>
            <p className="empty-hint">
              Run the management command to load sample content:
              <code>python manage.py load_sample_content</code>
            </p>
          </div>
        ) : (
          domains.map(domain => (
            <div
              key={domain.id}
              className={`domain-item ${domain.is_active ? 'active' : ''}`}
            >
              <div className="domain-info">
                <div className="domain-name">
                  {domain.name}
                  {domain.is_active && (
                    <Check className="active-icon" size={16} />
                  )}
                </div>
                <div className="domain-description">
                  {domain.description}
                </div>
              </div>
              
              {!domain.is_active && (
                <button
                  className="switch-button"
                  onClick={() => switchDomain(domain.slug)}
                  disabled={switching}
                >
                  {switching ? (
                    <RefreshCw className="spinner" size={14} />
                  ) : (
                    'Switch'
                  )}
                </button>
              )}
            </div>
          ))
        )}
      </div>

      <div className="domain-info-panel">
        <h4>About Content Domains</h4>
        <p>
          Content domains define the knowledge base and context for AI interactions. 
          Each domain provides specialized content and guidance approaches.
        </p>
        <ul>
          <li><strong>Biblical Texts:</strong> Spiritual guidance and biblical wisdom</li>
          <li><strong>Buddhist Teachings:</strong> Mindfulness and philosophical insights</li>
          <li><strong>Self-Help:</strong> Personal development and motivation</li>
        </ul>
        <p className="domain-note">
          <strong>Note:</strong> The application is designed to be modular - 
          new domains can be easily added without changing the core code.
        </p>
      </div>
    </div>
  )
}

export default DomainSelector
