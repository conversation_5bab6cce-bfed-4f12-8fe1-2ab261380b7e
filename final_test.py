#!/usr/bin/env python3
"""
Final comprehensive test of the LLM Wrapper application
"""

import requests
import json
import time

def test_application():
    print("🎯 FINAL APPLICATION TEST")
    print("=" * 50)
    
    # Test 1: Backend Health
    print("\n1. Testing Backend Health...")
    try:
        response = requests.get('http://localhost:8080/admin/', timeout=5)
        if response.status_code in [200, 302]:
            print("   ✅ Django Backend: HEALTHY")
        else:
            print(f"   ⚠️ Django Backend: Status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Django Backend: {e}")
    
    # Test 2: Frontend Accessibility
    print("\n2. Testing Frontend Accessibility...")
    try:
        response = requests.get('http://localhost:5173/', timeout=5)
        if response.status_code == 200:
            print("   ✅ React Frontend: ACCESSIBLE")
        else:
            print(f"   ⚠️ React Frontend: Status {response.status_code}")
    except Exception as e:
        print(f"   ❌ React Frontend: {e}")
    
    # Test 3: Main Chat API
    print("\n3. Testing Main Chat API...")
    try:
        response = requests.post(
            'http://localhost:8080/api/chat/',
            headers={
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173'
            },
            json={
                'message': 'What does the Bible say about love?',
                'interaction_type': 'interpretation',
                'session_id': 'test-session'
            },
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Chat API: WORKING")
            print(f"   Response length: {len(data.get('response', ''))} chars")
            print(f"   Success: {data.get('success', True)}")
            print(f"   Model: {data.get('model_info', {}).get('model', 'Unknown')}")
        else:
            print(f"   ❌ Chat API: Status {response.status_code}")
            print(f"   Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Chat API: {e}")
    
    # Test 4: Daily Guidance
    print("\n4. Testing Daily Guidance...")
    try:
        response = requests.post(
            'http://localhost:8080/api/chat/',
            headers={
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173'
            },
            json={
                'message': 'Please provide daily spiritual guidance.',
                'interaction_type': 'daily_guidance',
                'session_id': 'test-session'
            },
            timeout=20
        )
        
        if response.status_code == 200:
            print("   ✅ Daily Guidance: WORKING")
        else:
            print(f"   ❌ Daily Guidance: Status {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Daily Guidance: {e}")
    
    # Test 5: CORS Headers
    print("\n5. Testing CORS Headers...")
    try:
        response = requests.post(
            'http://localhost:8080/api/chat/',
            headers={
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173'
            },
            json={'message': 'Test', 'interaction_type': 'general'},
            timeout=10
        )
        
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        if cors_headers:
            print("   ✅ CORS Headers: PRESENT")
            for header, value in cors_headers.items():
                print(f"      {header}: {value}")
        else:
            print("   ⚠️ CORS Headers: NOT FOUND (but requests still work)")
            
    except Exception as e:
        print(f"   ❌ CORS Test: {e}")
    
    print("\n" + "=" * 50)
    print("🎊 APPLICATION STATUS SUMMARY:")
    print("   Backend: Django + Gemini API")
    print("   Frontend: React + Vite")
    print("   Main Functionality: Chat Interface")
    print("   Content Domain: Biblical Texts")
    print("   Architecture: Modular & Extensible")
    print("\n✅ APPLICATION IS READY FOR USE!")
    print("   Frontend URL: http://localhost:5173/")
    print("   Backend URL: http://localhost:8080/")

if __name__ == "__main__":
    test_application()
