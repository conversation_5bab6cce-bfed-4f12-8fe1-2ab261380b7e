import { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2, Volume<PERSON>, Loader, Play, Square } from 'lucide-react'
import axios from 'axios'
import './SpeechInterface.css'

const API_BASE_URL = 'http://localhost:8081/api'  // Using CORS proxy

const SpeechInterface = ({ sessionId }) => {
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [audioBlob, setAudioBlob] = useState(null)
  const [transcribedText, setTranscribedText] = useState('')
  const [aiResponse, setAiResponse] = useState('')
  const [interactionType, setInteractionType] = useState('general')
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [speechEnabled, setSpeechEnabled] = useState(true)
  
  const mediaRecorderRef = useRef(null)
  const audioChunksRef = useRef([])
  const speechSynthesisRef = useRef(null)

  useEffect(() => {
    // Check if speech synthesis is available
    if ('speechSynthesis' in window) {
      speechSynthesisRef.current = window.speechSynthesis
    } else {
      setSpeechEnabled(false)
    }

    return () => {
      // Cleanup
      if (speechSynthesisRef.current) {
        speechSynthesisRef.current.cancel()
      }
    }
  }, [])

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      
      mediaRecorderRef.current = new MediaRecorder(stream)
      audioChunksRef.current = []

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        setAudioBlob(audioBlob)
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorderRef.current.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const processAudio = async () => {
    if (!audioBlob) return

    setIsProcessing(true)
    setTranscribedText('')
    setAiResponse('')

    try {
      const formData = new FormData()
      formData.append('audio_file', audioBlob, 'recording.wav')
      formData.append('interaction_type', interactionType)
      formData.append('session_id', sessionId)

      const response = await axios.post(`${API_BASE_URL}/speech/chat/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      setTranscribedText(response.data.transcribed_text)
      setAiResponse(response.data.ai_response)

      // Automatically speak the response if speech is enabled
      if (speechEnabled && response.data.ai_response) {
        speakText(response.data.ai_response)
      }

    } catch (error) {
      console.error('Error processing audio:', error)
      setAiResponse('Sorry, I encountered an error processing your audio. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const speakText = (text) => {
    if (!speechSynthesisRef.current || !speechEnabled) return

    // Cancel any ongoing speech
    speechSynthesisRef.current.cancel()

    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = 0.9
    utterance.pitch = 1
    utterance.volume = 0.8

    utterance.onstart = () => setIsSpeaking(true)
    utterance.onend = () => setIsSpeaking(false)
    utterance.onerror = () => setIsSpeaking(false)

    speechSynthesisRef.current.speak(utterance)
  }

  const stopSpeaking = () => {
    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.cancel()
      setIsSpeaking(false)
    }
  }

  const clearSession = () => {
    setAudioBlob(null)
    setTranscribedText('')
    setAiResponse('')
    stopSpeaking()
  }

  const interactionTypes = [
    { value: 'general', label: 'General' },
    { value: 'daily_guidance', label: 'Daily Guidance' },
    { value: 'interpretation', label: 'Interpretation' },
    { value: 'therapeutic', label: 'Therapeutic' },
  ]

  return (
    <div className="speech-interface">
      <div className="speech-header">
        <h2>Speech Interface</h2>
        <p>Speak your question and receive audio responses</p>
      </div>

      <div className="speech-controls">
        <div className="interaction-selector">
          <label>Interaction Type:</label>
          <select 
            value={interactionType} 
            onChange={(e) => setInteractionType(e.target.value)}
            disabled={isRecording || isProcessing}
          >
            {interactionTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div className="speech-settings">
          <label className="speech-toggle">
            <input
              type="checkbox"
              checked={speechEnabled}
              onChange={(e) => setSpeechEnabled(e.target.checked)}
            />
            Enable Text-to-Speech
          </label>
        </div>
      </div>

      <div className="recording-section">
        <div className="recording-controls">
          {!isRecording ? (
            <button
              className="record-button"
              onClick={startRecording}
              disabled={isProcessing}
            >
              <Mic size={24} />
              Start Recording
            </button>
          ) : (
            <button
              className="record-button recording"
              onClick={stopRecording}
            >
              <Square size={24} />
              Stop Recording
            </button>
          )}

          {audioBlob && !isRecording && (
            <button
              className="process-button"
              onClick={processAudio}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader className="spinner" size={20} />
                  Processing...
                </>
              ) : (
                <>
                  <Play size={20} />
                  Process Audio
                </>
              )}
            </button>
          )}

          {(transcribedText || aiResponse) && (
            <button
              className="clear-button"
              onClick={clearSession}
              disabled={isRecording || isProcessing}
            >
              Clear
            </button>
          )}
        </div>

        {isRecording && (
          <div className="recording-indicator">
            <div className="pulse-dot"></div>
            Recording...
          </div>
        )}
      </div>

      {transcribedText && (
        <div className="transcription-section">
          <h3>What you said:</h3>
          <div className="transcription-text">
            {transcribedText}
          </div>
        </div>
      )}

      {aiResponse && (
        <div className="response-section">
          <div className="response-header">
            <h3>AI Response:</h3>
            <div className="audio-controls">
              {!isSpeaking ? (
                <button
                  className="speak-button"
                  onClick={() => speakText(aiResponse)}
                  disabled={!speechEnabled}
                  title="Speak response"
                >
                  <Volume2 size={20} />
                </button>
              ) : (
                <button
                  className="stop-speak-button"
                  onClick={stopSpeaking}
                  title="Stop speaking"
                >
                  <VolumeX size={20} />
                </button>
              )}
            </div>
          </div>
          <div className="response-text">
            {aiResponse}
          </div>
          {isSpeaking && (
            <div className="speaking-indicator">
              <div className="audio-wave"></div>
              Speaking...
            </div>
          )}
        </div>
      )}

      {isProcessing && (
        <div className="processing-indicator">
          <Loader className="spinner" size={24} />
          <p>Processing your audio...</p>
        </div>
      )}
    </div>
  )
}

export default SpeechInterface
