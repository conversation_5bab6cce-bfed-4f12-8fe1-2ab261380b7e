import { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2, Volume<PERSON>, Loader, Play, Square } from 'lucide-react'
import axios from 'axios'
import './SpeechInterface.css'

const API_BASE_URL = 'http://localhost:8081/api'  // Using CORS proxy

const SpeechInterface = ({ sessionId }) => {
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [audioBlob, setAudioBlob] = useState(null)
  const [audioUrl, setAudioUrl] = useState(null)
  const [transcribedText, setTranscribedText] = useState('')
  const [aiResponse, setAiResponse] = useState('')
  const [interactionType, setInteractionType] = useState('general')
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [speechEnabled, setSpeechEnabled] = useState(true)
  const [isPlayingRecording, setIsPlayingRecording] = useState(false)

  const mediaRecorderRef = useRef(null)
  const audioChunksRef = useRef([])
  const speechSynthesisRef = useRef(null)
  const audioPlayerRef = useRef(null)

  useEffect(() => {
    // Check if speech synthesis is available
    if ('speechSynthesis' in window) {
      speechSynthesisRef.current = window.speechSynthesis
    } else {
      setSpeechEnabled(false)
    }

    return () => {
      // Cleanup
      if (speechSynthesisRef.current) {
        speechSynthesisRef.current.cancel()
      }
    }
  }, [])

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      
      mediaRecorderRef.current = new MediaRecorder(stream)
      audioChunksRef.current = []

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        setAudioBlob(audioBlob)

        // Create audio URL for playback
        const url = URL.createObjectURL(audioBlob)
        setAudioUrl(url)

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorderRef.current.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const playRecording = () => {
    if (audioUrl && audioPlayerRef.current) {
      setIsPlayingRecording(true)
      audioPlayerRef.current.play()
    }
  }

  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause()
      audioPlayerRef.current.currentTime = 0
      setIsPlayingRecording(false)
    }
  }

  const processAudio = async () => {
    if (!audioBlob) return

    setIsProcessing(true)
    setTranscribedText('')
    setAiResponse('')

    try {
      const formData = new FormData()
      formData.append('audio_file', audioBlob, 'recording.wav')
      formData.append('interaction_type', interactionType)
      formData.append('session_id', sessionId)

      // First test the speech endpoint
      const testResponse = await fetch(`${API_BASE_URL}/speech/test/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Testing speech endpoint'
        })
      })

      if (!testResponse.ok) {
        throw new Error(`Speech test failed: ${testResponse.status}`)
      }

      console.log('Speech test successful')

      // For now, simulate speech processing with the chat API
      const chatResponse = await fetch(`${API_BASE_URL}/chat/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Please provide a response as if this was transcribed from speech',
          interaction_type: interactionType,
          session_id: sessionId
        })
      })

      if (!chatResponse.ok) {
        throw new Error(`Chat API failed: ${chatResponse.status}`)
      }

      const chatData = await chatResponse.json()

      // Simulate transcription
      setTranscribedText('Audio transcribed successfully (simulated)')
      setAiResponse(chatData.response || '')

      // If speech synthesis is enabled and available, speak the response
      if (speechEnabled && chatData.response) {
        speakText(chatData.response)
      }

    } catch (error) {
      console.error('Error processing audio:', error)
      setTranscribedText('Error: Could not process audio')
      setAiResponse('Sorry, there was an error processing your audio. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const speakText = (text) => {
    if (!speechSynthesisRef.current || !speechEnabled) return

    // Cancel any ongoing speech
    speechSynthesisRef.current.cancel()

    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = 0.9
    utterance.pitch = 1
    utterance.volume = 0.8

    utterance.onstart = () => setIsSpeaking(true)
    utterance.onend = () => setIsSpeaking(false)
    utterance.onerror = () => setIsSpeaking(false)

    speechSynthesisRef.current.speak(utterance)
  }

  const stopSpeaking = () => {
    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.cancel()
      setIsSpeaking(false)
    }
  }

  const clearSession = () => {
    setAudioBlob(null)
    setTranscribedText('')
    setAiResponse('')
    stopSpeaking()
  }

  const interactionTypes = [
    { value: 'general', label: 'General' },
    { value: 'daily_guidance', label: 'Daily Guidance' },
    { value: 'interpretation', label: 'Interpretation' },
    { value: 'therapeutic', label: 'Therapeutic' },
  ]

  return (
    <div className="speech-interface">
      <div className="speech-header">
        <h2>Speech Interface</h2>
        <p>Speak your question and receive audio responses</p>
      </div>

      <div className="speech-controls">
        <div className="interaction-selector">
          <label>Interaction Type:</label>
          <select 
            value={interactionType} 
            onChange={(e) => setInteractionType(e.target.value)}
            disabled={isRecording || isProcessing}
          >
            {interactionTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div className="speech-settings">
          <label className="speech-toggle">
            <input
              type="checkbox"
              checked={speechEnabled}
              onChange={(e) => setSpeechEnabled(e.target.checked)}
            />
            Enable Text-to-Speech
          </label>
        </div>
      </div>

      <div className="recording-section">
        <div className="recording-controls">
          {!isRecording ? (
            <button
              className="record-button"
              onClick={startRecording}
              disabled={isProcessing}
            >
              <Mic size={24} />
              Start Recording
            </button>
          ) : (
            <button
              className="record-button recording"
              onClick={stopRecording}
            >
              <Square size={24} />
              Stop Recording
            </button>
          )}

          {audioBlob && !isRecording && (
            <>
              <button
                className="playback-button"
                onClick={isPlayingRecording ? stopPlayback : playRecording}
                disabled={!audioUrl}
              >
                {isPlayingRecording ? (
                  <>
                    <Square size={20} />
                    Stop Playback
                  </>
                ) : (
                  <>
                    <Play size={20} />
                    Hear Recording
                  </>
                )}
              </button>

              <button
                className="process-button"
                onClick={processAudio}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader className="spinner" size={20} />
                    Processing...
                  </>
                ) : (
                  <>
                    <Play size={20} />
                    Process Audio
                  </>
                )}
              </button>
            </>
          )}

          {(transcribedText || aiResponse) && (
            <button
              className="clear-button"
              onClick={clearSession}
              disabled={isRecording || isProcessing}
            >
              Clear
            </button>
          )}
        </div>

        {isRecording && (
          <div className="recording-indicator">
            <div className="pulse-dot"></div>
            Recording...
          </div>
        )}
      </div>

      {transcribedText && (
        <div className="transcription-section">
          <h3>What you said:</h3>
          <div className="transcription-text">
            {transcribedText}
          </div>
        </div>
      )}

      {aiResponse && (
        <div className="response-section">
          <div className="response-header">
            <h3>AI Response:</h3>
            <div className="audio-controls">
              {!isSpeaking ? (
                <button
                  className="speak-button"
                  onClick={() => speakText(aiResponse)}
                  disabled={!speechEnabled}
                  title="Speak response"
                >
                  <Volume2 size={20} />
                </button>
              ) : (
                <button
                  className="stop-speak-button"
                  onClick={stopSpeaking}
                  title="Stop speaking"
                >
                  <VolumeX size={20} />
                </button>
              )}
            </div>
          </div>
          <div className="response-text">
            {aiResponse}
          </div>
          {isSpeaking && (
            <div className="speaking-indicator">
              <div className="audio-wave"></div>
              Speaking...
            </div>
          )}
        </div>
      )}

      {isProcessing && (
        <div className="processing-indicator">
          <Loader className="spinner" size={24} />
          <p>Processing your audio...</p>
        </div>
      )}

      {/* Hidden audio element for playback */}
      <audio
        ref={audioPlayerRef}
        src={audioUrl}
        onEnded={() => setIsPlayingRecording(false)}
        style={{ display: 'none' }}
      />
    </div>
  )
}

export default SpeechInterface
