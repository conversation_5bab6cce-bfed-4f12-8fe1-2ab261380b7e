import { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2, VolumeX, Loader, Play, Square } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import axios from 'axios'
import './SpeechInterface.css'

const API_BASE_URL = 'http://localhost:8081/api'  // Using CORS proxy

const SpeechInterface = ({ sessionId }) => {
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [audioBlob, setAudioBlob] = useState(null)
  const [audioUrl, setAudioUrl] = useState(null)
  const [transcribedText, setTranscribedText] = useState('')
  const [aiResponse, setAiResponse] = useState('')
  const [interactionType, setInteractionType] = useState('general')
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [speechEnabled, setSpeechEnabled] = useState(true)
  const [isPlayingRecording, setIsPlayingRecording] = useState(false)
  const [isTranscribing, setIsTranscribing] = useState(false)

  const mediaRecorderRef = useRef(null)
  const audioChunksRef = useRef([])
  const speechSynthesisRef = useRef(null)
  const audioPlayerRef = useRef(null)
  const speechRecognitionRef = useRef(null)
  const currentTranscriptRef = useRef('')

  useEffect(() => {
    // Check if speech synthesis is available
    if ('speechSynthesis' in window) {
      speechSynthesisRef.current = window.speechSynthesis
    } else {
      setSpeechEnabled(false)
    }

    // Initialize Web Speech API for speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      speechRecognitionRef.current = new SpeechRecognition()

      speechRecognitionRef.current.continuous = true
      speechRecognitionRef.current.interimResults = true
      speechRecognitionRef.current.lang = 'en-US'

      speechRecognitionRef.current.onresult = (event) => {
        let finalTranscript = ''
        let interimTranscript = ''

        // Process all results
        for (let i = 0; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript + ' '
          } else {
            interimTranscript += transcript
          }
        }

        // Update transcribed text with final results
        if (finalTranscript.trim()) {
          const cleanTranscript = finalTranscript.trim()
          console.log('🎤 Final Speech Recognition Result:', cleanTranscript)
          currentTranscriptRef.current = cleanTranscript
          setTranscribedText(cleanTranscript)
        }

        // Show interim results for user feedback (optional)
        if (interimTranscript.trim()) {
          console.log('🎤 Interim Speech Recognition:', interimTranscript.trim())
        }
      }

      speechRecognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error)
        setIsTranscribing(false)
      }

      speechRecognitionRef.current.onend = () => {
        console.log('🎤 Speech recognition ended')
        setIsTranscribing(false)

        // Auto-process transcribed text when speech recognition ends
        setTimeout(() => {
          const currentText = currentTranscriptRef.current.trim()
          if (currentText) {
            console.log('🤖 Auto-processing transcribed text:', currentText)
            processTranscribedText(currentText)
          } else {
            console.log('⚠️ No transcribed text to process')
          }
        }, 500) // Small delay to ensure state is updated
      }
    }

    return () => {
      // Cleanup
      if (speechSynthesisRef.current) {
        speechSynthesisRef.current.cancel()
      }
      if (speechRecognitionRef.current) {
        speechRecognitionRef.current.stop()
      }
    }
  }, [])

  const resetSpeechRecognition = () => {
    console.log('🔄 Resetting speech recognition...')

    // Stop any ongoing speech recognition
    if (speechRecognitionRef.current && isTranscribing) {
      speechRecognitionRef.current.stop()
    }

    // Clear transcribed text and refs
    currentTranscriptRef.current = ''
    setTranscribedText('')
    setAiResponse('')
    setIsTranscribing(false)

    console.log('🔄 Speech recognition reset complete')
  }

  const startRecording = async () => {
    try {
      // Reset speech recognition and clear all previous data
      resetSpeechRecognition()

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      mediaRecorderRef.current = new MediaRecorder(stream)
      audioChunksRef.current = []

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        setAudioBlob(audioBlob)

        // Create audio URL for playback
        const url = URL.createObjectURL(audioBlob)
        setAudioUrl(url)

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())

        // The transcribed text will be processed automatically when speech recognition ends
        // via the onend handler above
      }

      mediaRecorderRef.current.start()
      setIsRecording(true)

      // Start speech recognition with a fresh instance
      if (speechRecognitionRef.current) {
        try {
          setIsTranscribing(true)
          speechRecognitionRef.current.start()
        } catch (error) {
          console.error('Error starting speech recognition:', error)
          // If there's an error, try to reset and start again
          setTimeout(() => {
            if (speechRecognitionRef.current) {
              try {
                speechRecognitionRef.current.start()
              } catch (retryError) {
                console.error('Retry failed:', retryError)
                setIsTranscribing(false)
              }
            }
          }, 100)
        }
      }
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }

    // Stop speech recognition
    if (speechRecognitionRef.current && isTranscribing) {
      speechRecognitionRef.current.stop()
      setIsTranscribing(false)
    }
  }

  const playRecording = () => {
    if (audioUrl && audioPlayerRef.current) {
      setIsPlayingRecording(true)
      audioPlayerRef.current.play()
    }
  }

  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause()
      audioPlayerRef.current.currentTime = 0
      setIsPlayingRecording(false)
    }
  }

  const processTranscribedText = async (text) => {
    if (!text.trim()) {
      console.log('No text to process')
      return
    }

    console.log('Processing transcribed text:', text)
    setIsProcessing(true)

    try {
      // Send the transcribed text to Gemini via the chat API
      const chatResponse = await fetch(`${API_BASE_URL}/chat/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: text,
          interaction_type: interactionType,
          session_id: sessionId
        })
      })

      if (!chatResponse.ok) {
        throw new Error(`Chat API failed: ${chatResponse.status}`)
      }

      const chatData = await chatResponse.json()
      const aiResponseText = chatData.response || ''

      console.log('Received AI response:', aiResponseText.substring(0, 100) + '...')
      setAiResponse(aiResponseText)

      // Automatically speak the AI response
      if (speechEnabled && aiResponseText) {
        console.log('Converting AI response to speech...')
        speakText(aiResponseText)
      } else {
        console.log('Speech synthesis disabled or no response to speak')
      }

    } catch (error) {
      console.error('Error processing transcribed text:', error)
      const errorMessage = 'Sorry, there was an error processing your message. Please try again.'
      setAiResponse(errorMessage)

      // Speak the error message too
      if (speechEnabled) {
        speakText(errorMessage)
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const processAudio = async () => {
    if (!audioBlob) return

    setIsProcessing(true)
    setTranscribedText('')
    setAiResponse('')

    try {
      const formData = new FormData()
      formData.append('audio_file', audioBlob, 'recording.wav')
      formData.append('interaction_type', interactionType)
      formData.append('session_id', sessionId)

      // First test the speech endpoint
      const testResponse = await fetch(`${API_BASE_URL}/speech/test/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Testing speech endpoint'
        })
      })

      if (!testResponse.ok) {
        throw new Error(`Speech test failed: ${testResponse.status}`)
      }

      console.log('Speech test successful')

      // For now, simulate speech processing with the chat API
      const chatResponse = await fetch(`${API_BASE_URL}/chat/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Please provide a response as if this was transcribed from speech',
          interaction_type: interactionType,
          session_id: sessionId
        })
      })

      if (!chatResponse.ok) {
        throw new Error(`Chat API failed: ${chatResponse.status}`)
      }

      const chatData = await chatResponse.json()

      // Simulate transcription
      setTranscribedText('Audio transcribed successfully (simulated)')
      setAiResponse(chatData.response || '')

      // If speech synthesis is enabled and available, speak the response
      if (speechEnabled && chatData.response) {
        speakText(chatData.response)
      }

    } catch (error) {
      console.error('Error processing audio:', error)
      setTranscribedText('Error: Could not process audio')
      setAiResponse('Sorry, there was an error processing your audio. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const speakText = (text) => {
    if (!speechSynthesisRef.current || !speechEnabled) {
      console.log('Speech synthesis not available or disabled')
      return
    }

    console.log('🔊 Starting text-to-speech for:', text.substring(0, 50) + '...')

    // Cancel any ongoing speech
    speechSynthesisRef.current.cancel()

    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = 0.8  // Slightly slower for better understanding
    utterance.pitch = 1
    utterance.volume = 0.9

    utterance.onstart = () => {
      console.log('🔊 Text-to-speech started')
      setIsSpeaking(true)
    }

    utterance.onend = () => {
      console.log('🔊 Text-to-speech completed')
      setIsSpeaking(false)
    }

    utterance.onerror = (event) => {
      console.error('🔊 Speech synthesis error:', event.error)
      setIsSpeaking(false)
    }

    // Add a small delay to ensure the speech synthesis is ready
    setTimeout(() => {
      speechSynthesisRef.current.speak(utterance)
    }, 100)
  }

  const stopSpeaking = () => {
    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.cancel()
      setIsSpeaking(false)
    }
  }

  const clearSession = () => {
    setAudioBlob(null)
    setAudioUrl(null)
    setTranscribedText('')
    setAiResponse('')
    setIsTranscribing(false)
    stopSpeaking()

    // Stop any ongoing speech recognition
    if (speechRecognitionRef.current && isTranscribing) {
      speechRecognitionRef.current.stop()
    }
  }

  const interactionTypes = [
    { value: 'general', label: 'General' },
    { value: 'daily_guidance', label: 'Daily Guidance' },
    { value: 'interpretation', label: 'Interpretation' },
    { value: 'therapeutic', label: 'Therapeutic' },
  ]

  return (
    <div className="speech-interface">
      <div className="speech-header">
        <h2>Speech Interface</h2>
        <p>Speak your question and receive audio responses</p>
      </div>

      <div className="speech-controls">
        <div className="interaction-selector">
          <label>Interaction Type:</label>
          <select 
            value={interactionType} 
            onChange={(e) => setInteractionType(e.target.value)}
            disabled={isRecording || isProcessing}
          >
            {interactionTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div className="speech-settings">
          <label className="speech-toggle">
            <input
              type="checkbox"
              checked={speechEnabled}
              onChange={(e) => setSpeechEnabled(e.target.checked)}
            />
            Enable Text-to-Speech
          </label>
        </div>
      </div>

      <div className="recording-section">
        <div className="recording-controls">
          {!isRecording ? (
            <button
              className="record-button"
              onClick={startRecording}
              disabled={isProcessing}
            >
              <Mic size={24} />
              Start Recording
            </button>
          ) : (
            <button
              className="record-button recording"
              onClick={stopRecording}
            >
              <Square size={24} />
              Stop Recording
            </button>
          )}

          {audioBlob && !isRecording && (
            <>
              <button
                className="playback-button"
                onClick={isPlayingRecording ? stopPlayback : playRecording}
                disabled={!audioUrl}
              >
                {isPlayingRecording ? (
                  <>
                    <Square size={20} />
                    Stop Playback
                  </>
                ) : (
                  <>
                    <Play size={20} />
                    Hear Recording
                  </>
                )}
              </button>

              <button
                className="process-button"
                onClick={processAudio}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader className="spinner" size={20} />
                    Processing...
                  </>
                ) : (
                  <>
                    <Play size={20} />
                    Process Audio
                  </>
                )}
              </button>
            </>
          )}

          {(transcribedText || aiResponse) && (
            <button
              className="clear-button"
              onClick={clearSession}
              disabled={isRecording || isProcessing}
            >
              Clear
            </button>
          )}
        </div>

        {isRecording && (
          <div className="recording-indicator">
            <div className="pulse-dot"></div>
            Recording...
          </div>
        )}

        {isTranscribing && (
          <div className="transcribing-indicator">
            <div className="pulse-dot"></div>
            Converting speech to text...
          </div>
        )}

        {isProcessing && (
          <div className="processing-indicator">
            <Loader className="spinner" size={20} />
            Getting AI response...
          </div>
        )}
      </div>

      {transcribedText && (
        <div className="transcription-section">
          <h3>What you said:</h3>
          <div className="transcription-text">
            {transcribedText}
          </div>
        </div>
      )}

      {aiResponse && (
        <div className="response-section">
          <div className="response-header">
            <h3>AI Response:</h3>
            <div className="audio-controls">
              {!isSpeaking ? (
                <button
                  className="speak-button"
                  onClick={() => speakText(aiResponse)}
                  disabled={!speechEnabled}
                  title="Speak response"
                >
                  <Volume2 size={20} />
                </button>
              ) : (
                <button
                  className="stop-speak-button"
                  onClick={stopSpeaking}
                  title="Stop speaking"
                >
                  <VolumeX size={20} />
                </button>
              )}
            </div>
          </div>
          <div className="response-text">
            <ReactMarkdown>{aiResponse}</ReactMarkdown>
          </div>
          {isSpeaking && (
            <div className="speaking-indicator">
              <div className="audio-wave"></div>
              Speaking...
            </div>
          )}
        </div>
      )}

      {isProcessing && (
        <div className="processing-indicator">
          <Loader className="spinner" size={24} />
          <p>Processing your audio...</p>
        </div>
      )}

      {/* Hidden audio element for playback */}
      <audio
        ref={audioPlayerRef}
        src={audioUrl}
        onEnded={() => setIsPlayingRecording(false)}
        style={{ display: 'none' }}
      />
    </div>
  )
}

export default SpeechInterface
