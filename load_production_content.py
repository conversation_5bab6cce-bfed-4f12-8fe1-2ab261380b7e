#!/usr/bin/env python3
"""
Load content into production database via API
"""

import requests
import json
import time

def load_content():
    """Load content into production database"""
    
    backend_url = "https://ai-powered-wisdom-backend.onrender.com"
    
    print("🚀 Loading content into production database...")
    print(f"Backend URL: {backend_url}")
    print("-" * 50)
    
    # Test health first
    print("1. Testing backend health...")
    try:
        health_response = requests.get(f"{backend_url}/api/chat/health/", timeout=10)
        if health_response.status_code == 200:
            print("✅ Backend is healthy")
            health_data = health_response.json()
            print(f"   LLM Status: {health_data.get('llm_providers', {})}")
            print(f"   Content Status: {health_data.get('content_service', {})}")
        else:
            print(f"❌ Backend health check failed: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {str(e)}")
        return False
    
    # Load content
    print("\n2. Loading content domains...")
    try:
        load_response = requests.post(f"{backend_url}/api/chat/load-content/", timeout=60)
        
        if load_response.status_code == 200:
            print("✅ Content loading successful!")
            data = load_response.json()
            print(f"   Domains loaded: {data.get('domains_loaded', [])}")
            print(f"   Total domains: {data.get('total_domains', 0)}")
            print(f"   Total content pieces: {data.get('total_content_pieces', 0)}")
            
            if data.get('output'):
                print(f"   Command output: {data.get('output')}")
                
        else:
            print(f"❌ Content loading failed: {load_response.status_code}")
            print(f"   Response: {load_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Content loading error: {str(e)}")
        return False
    
    # Test domains endpoint
    print("\n3. Testing domains endpoint...")
    try:
        domains_response = requests.get(f"{backend_url}/api/domains/", timeout=10)
        if domains_response.status_code == 200:
            domains = domains_response.json()
            print(f"✅ Found {len(domains)} domains:")
            for domain in domains:
                print(f"   - {domain.get('name', 'Unknown')} ({domain.get('slug', 'no-slug')})")
        else:
            print(f"❌ Domains endpoint failed: {domains_response.status_code}")
            
    except Exception as e:
        print(f"❌ Domains test error: {str(e)}")
    
    # Test chat endpoint
    print("\n4. Testing chat endpoint...")
    try:
        chat_data = {
            'message': 'Hello, this is a test message',
            'interaction_type': 'general',
            'domain_slug': 'biblical_texts'
        }
        
        chat_response = requests.post(
            f"{backend_url}/api/chat/", 
            json=chat_data,
            timeout=30
        )
        
        if chat_response.status_code == 200:
            print("✅ Chat endpoint working!")
            response_data = chat_response.json()
            print(f"   Response length: {len(response_data.get('response', ''))} characters")
        else:
            print(f"❌ Chat endpoint failed: {chat_response.status_code}")
            
    except Exception as e:
        print(f"❌ Chat test error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 Production setup complete!")
    print(f"🌐 Your backend is live at: {backend_url}")
    print("📝 Next step: Deploy frontend to Vercel")
    
    return True

if __name__ == "__main__":
    load_content()
