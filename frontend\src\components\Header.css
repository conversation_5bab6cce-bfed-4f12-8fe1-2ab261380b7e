.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-background {
  position: relative;
}

.header-pattern {
  /* Simple pattern */
}

.header-glow {
  /* Simple glow */
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: #ffd700;
  border-radius: 50%;
  color: #1a365d;
}

.logo-accent {
  position: absolute;
  top: -3px;
  right: -3px;
  background: #e53e3e;
  border-radius: 50%;
  padding: 3px;
  color: white;
}

.logo-text h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.logo-subtitle {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.header-verse {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.verse-star {
  color: #ffd700;
}

.verse-text {
  font-style: italic;
}

.verse-reference {
  font-weight: 600;
  color: #ffd700;
}

.domain-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 215, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  color: #ffd700;
}

.domain-name {
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-verse {
    flex-direction: column;
    gap: 0.25rem;
  }
}
