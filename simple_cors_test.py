#!/usr/bin/env python3
"""
Simple CORS test server to verify the issue
"""

from http.server import <PERSON><PERSON>PServer, BaseHTTPRequestHandler
import json

class CORSHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Access-Control-Max-Age', '86400')
        self.end_headers()

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {
            'message': 'CORS test successful!',
            'received_data': post_data.decode('utf-8')
        }
        self.wfile.write(json.dumps(response).encode('utf-8'))

if __name__ == '__main__':
    server = HTTPServer(('localhost', 8081), CORSHandler)
    print("Simple CORS test server running on http://localhost:8081")
    print("This server properly handles CORS for comparison")
    server.serve_forever()
