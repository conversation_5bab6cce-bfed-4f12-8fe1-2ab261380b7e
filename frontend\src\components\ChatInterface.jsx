import { useState, useRef, useEffect } from 'react'
import { Send, Loader, Sun, BookOpen, MessageCircle, Heart } from 'lucide-react'
import axios from 'axios'
import './ChatInterface.css'

const API_BASE_URL = 'http://localhost:8000/api'

const ChatInterface = ({ sessionId }) => {
  const [messages, setMessages] = useState([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [interactionType, setInteractionType] = useState('general')
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const interactionTypes = [
    { value: 'general', label: 'General', icon: MessageCircle },
    { value: 'daily_guidance', label: 'Daily Guidance', icon: Sun },
    { value: 'interpretation', label: 'Interpretation', icon: BookOpen },
    { value: 'therapeutic', label: 'Therapeutic', icon: Heart },
  ]

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      const response = await axios.post(`${API_BASE_URL}/chat/`, {
        message: inputMessage,
        interaction_type: interactionType,
        session_id: sessionId
      })

      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: response.data.response,
        timestamp: new Date(),
        metadata: {
          interaction_type: response.data.interaction_type,
          response_time: response.data.response_time,
          model_info: response.data.model_info
        }
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      console.error('Chat error:', error)
      const errorMessage = {
        id: Date.now() + 1,
        type: 'error',
        content: 'Sorry, I encountered an error while processing your message. Please try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getDailyGuidance = async () => {
    setIsLoading(true)
    try {
      const response = await axios.post(`${API_BASE_URL}/chat/daily-guidance/`, {
        session_id: sessionId
      })

      const guidanceMessage = {
        id: Date.now(),
        type: 'ai',
        content: response.data.guidance,
        timestamp: new Date(),
        metadata: {
          type: 'daily_guidance',
          content_piece: response.data.content_piece
        }
      }

      setMessages(prev => [...prev, guidanceMessage])
    } catch (error) {
      console.error('Daily guidance error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <h2>Chat Interface</h2>
        <div className="interaction-types">
          {interactionTypes.map(type => {
            const IconComponent = type.icon
            return (
              <button
                key={type.value}
                className={`interaction-btn ${interactionType === type.value ? 'active' : ''}`}
                onClick={() => setInteractionType(type.value)}
                title={type.label}
              >
                <IconComponent size={16} />
                <span className="interaction-label">{type.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      <div className="quick-actions">
        <button className="quick-action-btn" onClick={getDailyGuidance}>
          <Sun size={16} />
          Get Daily Guidance
        </button>
      </div>

      <div className="messages-container">
        {messages.length === 0 && (
          <div className="welcome-message">
            <h3>Welcome to your AI Guidance Assistant</h3>
            <p>Ask me anything about spiritual guidance, biblical interpretation, or just have a conversation. Choose an interaction type above to get started.</p>
          </div>
        )}
        
        {messages.map(message => (
          <div key={message.id} className={`message ${message.type}`}>
            <div className="message-content">
              {message.content}
            </div>
            <div className="message-timestamp">
              {message.timestamp.toLocaleTimeString()}
            </div>
            {message.metadata && (
              <div className="message-metadata">
                {message.metadata.response_time && (
                  <span>Response time: {message.metadata.response_time.toFixed(2)}s</span>
                )}
              </div>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="message ai loading">
            <div className="message-content">
              <Loader className="spinner" size={16} />
              Thinking...
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        <textarea
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message here..."
          className="message-input"
          rows={3}
          disabled={isLoading}
        />
        <button
          onClick={handleSendMessage}
          disabled={!inputMessage.trim() || isLoading}
          className="send-button"
        >
          <Send size={20} />
        </button>
      </div>
    </div>
  )
}

export default ChatInterface
