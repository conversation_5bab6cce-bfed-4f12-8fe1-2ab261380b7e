.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.chat-header h2 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.interaction-types {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.interaction-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #4a5568;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.interaction-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.interaction-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.interaction-label {
  font-weight: 500;
}

.quick-actions {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #48bb78;
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.quick-action-btn:hover {
  background: #38a169;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #ffffff;
}

.welcome-message {
  text-align: center;
  padding: 3rem 2rem;
  color: #718096;
}

.welcome-message h3 {
  color: #2d3748;
  margin-bottom: 1rem;
}

.message {
  margin-bottom: 1rem;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
}

.message.user .message-content {
  background: #667eea;
  color: white;
  border-radius: 18px 18px 4px 18px;
}

.message.ai .message-content {
  background: #f7fafc;
  color: #2d3748;
  border-radius: 18px 18px 18px 4px;
  border: 1px solid #e2e8f0;
}

.message.error .message-content {
  background: #fed7d7;
  color: #c53030;
  border-radius: 18px 18px 18px 4px;
  border: 1px solid #feb2b2;
}

.message-content {
  padding: 0.75rem 1rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-timestamp {
  font-size: 0.75rem;
  color: #a0aec0;
  margin-top: 0.25rem;
  text-align: right;
}

.message.user .message-timestamp {
  text-align: right;
}

.message.ai .message-timestamp {
  text-align: left;
}

.message-metadata {
  font-size: 0.75rem;
  color: #718096;
  margin-top: 0.25rem;
}

.message.loading .message-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.input-container {
  display: flex;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.4;
  background: white;
}

.message-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input:disabled {
  background: #f7fafc;
  color: #a0aec0;
}

.send-button {
  padding: 0.75rem;
  background: #667eea;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  background: #5a67d8;
}

.send-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .chat-header {
    padding: 1rem;
  }
  
  .interaction-types {
    gap: 0.25rem;
  }
  
  .interaction-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .interaction-label {
    display: none;
  }
  
  .message {
    max-width: 90%;
  }
  
  .input-container {
    padding: 0.75rem;
  }
}
