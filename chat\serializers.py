"""
Chat API Serializers

Defines serializers for chat-related API endpoints.
"""

from rest_framework import serializers
from content_management.models import InteractionLog, ContentDomain


class ChatMessageSerializer(serializers.Serializer):
    """Serializer for incoming chat messages"""

    message = serializers.CharField(max_length=2000)
    interaction_type = serializers.ChoiceField(
        choices=[
            ('daily_guidance', 'Daily Guidance'),
            ('interpretation', 'Interpretation'),
            ('conversational', 'Conversational'),
            ('therapeutic', 'Therapeutic'),
            ('general', 'General'),
        ],
        default='general'
    )
    session_id = serializers.CharField(max_length=100, required=False, allow_blank=True)
    context_override = serializers.CharField(max_length=5000, required=False, allow_blank=True)
    domain_slug = serializers.CharField(max_length=100, required=False, default='biblical_texts')


class ChatResponseSerializer(serializers.Serializer):
    """Serializer for chat responses"""
    
    response = serializers.Char<PERSON><PERSON>()
    interaction_type = serializers.CharField()
    session_id = serializers.CharField()
    success = serializers.BooleanField()
    error = serializers.CharField(required=False, allow_blank=True)
    context_used = serializers.CharField(required=False, allow_blank=True)
    response_time = serializers.FloatField(required=False)
    model_info = serializers.DictField(required=False)


class DailyGuidanceSerializer(serializers.Serializer):
    """Serializer for daily guidance requests"""

    session_id = serializers.CharField(max_length=100, required=False, allow_blank=True)
    custom_request = serializers.CharField(max_length=500, required=False, allow_blank=True)
    domain_slug = serializers.CharField(max_length=100, required=False, default='biblical_texts')


class DailyGuidanceResponseSerializer(serializers.Serializer):
    """Serializer for daily guidance responses"""
    
    guidance = serializers.CharField()
    content_piece = serializers.DictField(required=False)
    session_id = serializers.CharField()
    success = serializers.BooleanField()
    error = serializers.CharField(required=False, allow_blank=True)


class InteractionLogSerializer(serializers.ModelSerializer):
    """Serializer for interaction logs"""
    
    class Meta:
        model = InteractionLog
        fields = [
            'id', 'session_id', 'interaction_type', 'user_input', 
            'ai_response', 'content_domain', 'context_used',
            'response_time', 'success', 'error_message', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ContentDomainSerializer(serializers.ModelSerializer):
    """Serializer for content domains"""
    
    class Meta:
        model = ContentDomain
        fields = ['id', 'name', 'slug', 'description', 'is_active']
        read_only_fields = ['id']


class DomainSwitchSerializer(serializers.Serializer):
    """Serializer for domain switching requests"""
    
    domain_slug = serializers.CharField(max_length=100)


class ChatHistorySerializer(serializers.Serializer):
    """Serializer for chat history requests"""
    
    session_id = serializers.CharField(max_length=100)
    limit = serializers.IntegerField(default=20, min_value=1, max_value=100)


class ChatHistoryResponseSerializer(serializers.Serializer):
    """Serializer for chat history responses"""
    
    interactions = InteractionLogSerializer(many=True)
    total_count = serializers.IntegerField()
    session_id = serializers.CharField()
