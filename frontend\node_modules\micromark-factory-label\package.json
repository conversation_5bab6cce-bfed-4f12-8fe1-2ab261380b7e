{"name": "micromark-factory-label", "version": "2.0.1", "description": "micromark factory to parse labels (found in media, definitions)", "license": "MIT", "keywords": ["micromark", "factory", "label"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-label", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["dev/", "index.d.ts.map", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"devlop": "^1.0.0", "micromark-util-character": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "scripts": {"build": "micromark-build"}, "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"logical-assignment-operators": "off", "max-params": "off", "unicorn/no-this-assignment": "off", "unicorn/prefer-code-point": "off"}}}